#!/bin/bash

# 测试resign命令的脚本
echo "开始测试IPA重签名功能..."

# 设置测试参数
TEST_BASE_PATH="/Users/<USER>/Desktop/辅助工具/OnetoolsIPAResign"
# IPA_PATH="${TEST_BASE_PATH}/IPA/OverSea_Distro_QRSL-IOS-Shipping_20250821_003711.ipa"
IPA_PATH="${TEST_BASE_PATH}/IPA/HTGame-IOS-Shipping.ipa"
#IPA_PATH="${TEST_BASE_PATH}/IPA/TL_DisHKAppStore_16_1_0_20250711174142.ipa"
#BUNDLE_ID="com.hottagames.ntetest"
BUNDLE_ID="com.pwrd.yh.ios"
PROVISION_PATH="" #"${TEST_BASE_PATH}/comlaohunewdemosdk.mobileprovision"
PROVISION_NAME="" #"com.laohunew.demosdk"
CERT_NAME="" #“Apple Development: yanshan liu (25UN2ZP5WW)”
CERT_TYPE="dev"
DISPLAY_NAME="" #"Neverness To Everness - NTE"
APP_VERSION="" #"2.0.0"
BUILD_VERSION="" #"10"
OUTPUT_PATH="" #"${TEST_BASE_PATH}/output.ipa"
APP_ICON="${TEST_BASE_PATH}/icon1024.png"
# APP_ICON="${TEST_BASE_PATH}/Assets.xcassets"
LAUNCHER_SCREEN="${TEST_BASE_PATH}/launcherImage.png"
ADDITIONAL_PLIST_PATH="" #"${TEST_BASE_PATH}/addConfig.plist"
REMOVE_PLIST_KEYS="" #"LSApplicationQueriesSchemes"
DELETE_FILES_PATH="" #PrivacyInfo.xcprivacy,zh-Hans.lproj/Main.strings"
ADD_FILES_PATH="" #"${TEST_BASE_PATH}/replaceLogoFiles"
# 可选的自定义entitlements.plist文件路径
# ENTITLEMENTS_PATH="/path/to/entitlements.plist"
APP_DEV_CONFIG="" #"appid=1000164,env=sdktest"
# 日志文件输出格式，console（控制台输出）或 file（输出到文件)
OUTPUT_TYPE="console"

# 检查输入文件是否存在
if [ ! -f "$IPA_PATH" ]; then
    echo "错误: IPA文件不存在: $IPA_PATH"
    exit 1
fi

# 删除之前的输出文件
if [ -f "$OUTPUT_PATH" ]; then
    rm "$OUTPUT_PATH"
    echo "删除之前的输出文件: $OUTPUT_PATH"
fi

# 执行重签名命令
echo "执行重签名命令..."

./onetools ipa resign \
        -p "$IPA_PATH" \
        --bundle-id "$BUNDLE_ID" \
        --provision "$PROVISION_PATH" \
        --cert "$CERT_NAME" \
        --display-name "$DISPLAY_NAME" \
        --app-version "$APP_VERSION" \
        --build-version "$BUILD_VERSION" \
        --app-icon "$APP_ICON" \
        --launch-screen "$LAUNCHER_SCREEN" \
        --additional-plist "$ADDITIONAL_PLIST_PATH" \
        --remove-plist-keys "$REMOVE_PLIST_KEYS" \
        --delete-files "$DELETE_FILES_PATH" \
        --add-files-path "$ADD_FILES_PATH" \
        --app-dev-config "$APP_DEV_CONFIG" \
        --cert-type "$CERT_TYPE" \
        --log-output "$OUTPUT_TYPE" \
        --keep-temp-dir 

        