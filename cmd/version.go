/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)

// versionCmd represents the version command
var OneToolsVersion = "1.1.2"
var CommitID string
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "onetools版本号",
	Long:  `onetools版本号`,
	Run: func(cmd *cobra.Command, args []string) {
		// 打印工具版本号
		fmt.Println("V" + OneToolsVersion + "_" + CommitID)
	},
}

func init() {
	rootCmd.AddCommand(versionCmd)
}
