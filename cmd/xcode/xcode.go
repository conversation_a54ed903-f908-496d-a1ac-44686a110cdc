package xcode

import (
	"fmt"

	"github.com/bitrise-io/go-xcode/xcodeproject/xcodeproj"
)

type ProductType string

const (
	ProductTypeApplication ProductType = "com.apple.product-type.application"
	ProductTypeFramework   ProductType = "com.apple.product-type.framework"
)

func GetXcodeProj(projPath string) xcodeproj.XcodeProj {
	config, err := xcodeproj.Open(projPath)
	if err != nil {
		panic(err)
	}
	return config
}

func SaveXcodeProj(config xcodeproj.XcodeProj) {
	config.Save()
}

// BuildSettingKeyValue 构建设置键值对结构体
type BuildSettingKeyValue struct {
	Key   string
	Value string
}

// GetXCodeBuildSettings 获取buildSettings
// projPath: pbxproj 文件路径
// targetName: target名
// configName: 配置名称，如 "Release", "Debug"
func GetXCodeBuildSettings(projPath, targetName, configName string, productType ProductType) (map[string]interface{}, error) {
	// 打开项目文件
	config := GetXcodeProj(projPath)

	// 获取项目中的所有对象
	objects, _ := config.RawProj.Object("objects")

	// 首先找到主应用目标的配置列表ID（通过productType区分）
	mainAppConfigListID := ""
	for _, v := range objects {
		result := v.(map[string]interface{})
		// 查找主应用目标（productType为application的目标）
		if result["isa"] == "PBXNativeTarget" {
			productType, _ := result["productType"].(string)
			name, _ := result["name"].(string)

			if productType == string(productType) && name == targetName {
				if configListID, ok := result["buildConfigurationList"].(string); ok {
					mainAppConfigListID = configListID
					break
				}
			}
		}
	}

	if mainAppConfigListID == "" {
		return nil, fmt.Errorf("未找到主应用目标的构建配置列表")
	}

	// 获取主应用的构建配置ID列表
	mainAppConfigIDs := make(map[string]bool)
	for objID, v := range objects {
		result := v.(map[string]interface{})
		if result["isa"] == "XCConfigurationList" && objID == mainAppConfigListID {
			if buildConfigs, ok := result["buildConfigurations"].([]interface{}); ok {
				for _, configID := range buildConfigs {
					if configIDStr, ok := configID.(string); ok {
						mainAppConfigIDs[configIDStr] = true
					}
				}
			}
			break
		}
	}

	// 遍历所有对象
	for objID, v := range objects {
		result := v.(map[string]interface{})

		// 检查是否是 XCBuildConfiguration 且名称匹配，并且属于主应用
		if result["isa"] == "XCBuildConfiguration" && result["name"] == configName {
			// 检查是否属于主应用的配置
			if !mainAppConfigIDs[objID] {
				continue
			}

			// 获取或创建 buildSettings
			buildSettings, ok := result["buildSettings"].(map[string]interface{})
			if ok {
				return buildSettings, nil
			}
		}
	}

	return nil, fmt.Errorf("获取主应用buildSettings失败")
}

// UpdateBuildSetting 更新指定配置名称的多个构建设置值
// configName: 配置名称，如 "Release", "Debug"
// keyValues: 构建设置的键值对数组
func UpdateBuildSetting(config xcodeproj.XcodeProj, targetName, configName string, keyValues []BuildSettingKeyValue, targetProductType ProductType) error {
	if len(keyValues) == 0 {
		return fmt.Errorf("键值对数组不能为空")
	}

	// 获取项目中的所有对象
	objects, _ := config.RawProj.Object("objects")

	// 首先找到主应用目标的配置列表ID（通过productType区分）
	mainAppConfigListID := ""
	for _, v := range objects {
		result := v.(map[string]interface{})
		// 查找主应用目标（productType为application的目标）
		if result["isa"] == "PBXNativeTarget" {
			productType, _ := result["productType"].(string)
			name, _ := result["name"].(string)

			if productType == string(targetProductType) && name == targetName {
				if configListID, ok := result["buildConfigurationList"].(string); ok {
					mainAppConfigListID = configListID
					break
				}
			}
		}
	}

	if mainAppConfigListID == "" {
		return fmt.Errorf("未找到主应用目标的构建配置列表")
	}

	// 获取主应用的构建配置ID列表
	mainAppConfigIDs := make(map[string]bool)
	for objID, v := range objects {
		result := v.(map[string]interface{})
		if result["isa"] == "XCConfigurationList" && objID == mainAppConfigListID {
			if buildConfigs, ok := result["buildConfigurations"].([]interface{}); ok {
				for _, configID := range buildConfigs {
					if configIDStr, ok := configID.(string); ok {
						mainAppConfigIDs[configIDStr] = true
					}
				}
			}
			break
		}
	}

	// 遍历所有对象，只更新主应用的配置
	updated := false
	for objID, v := range objects {
		result := v.(map[string]interface{})

		// 检查是否是 XCBuildConfiguration 且名称匹配，并且属于主应用
		if result["isa"] == "XCBuildConfiguration" && result["name"] == configName {
			// 检查是否属于主应用的配置
			if !mainAppConfigIDs[objID] {
				continue
			}

			// 获取或创建 buildSettings
			buildSettings, ok := result["buildSettings"].(map[string]interface{})
			if !ok {
				buildSettings = make(map[string]interface{})
				result["buildSettings"] = buildSettings
			}

			// 只有当配置中已存在 PRODUCT_BUNDLE_IDENTIFIER 时才进行修改
			if _, hasBundleID := buildSettings["PRODUCT_BUNDLE_IDENTIFIER"]; hasBundleID {
				// 批量设置所有键值对
				for _, kv := range keyValues {
					buildSettings[kv.Key] = kv.Value
				}
				updated = true
			}
		}
	}

	if !updated {
		return fmt.Errorf("未找到配置名称为 '%s' 的主应用构建配置或配置中不包含 PRODUCT_BUNDLE_IDENTIFIER", configName)
	}

	return nil
}

// UpdateAndSaveBuildSettings 批量更新构建设置并保存到文件
// projPath: pbxproj 文件路径
// configName: 配置名称，如 "Release", "Debug"
// keyValues: 构建设置的键值对数组
func UpdateAndSaveBuildSettings(projPath, targetName, configName string, keyValues []BuildSettingKeyValue, productType ProductType) error {
	// 打开项目文件
	config := GetXcodeProj(projPath)

	// 更新构建设置
	err := UpdateBuildSetting(config, targetName, configName, keyValues, productType)
	if err != nil {
		return err
	}

	// 保存文件
	SaveXcodeProj(config)
	return nil
}
