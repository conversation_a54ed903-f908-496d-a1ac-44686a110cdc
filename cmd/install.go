/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"log"
	"onetools/cmd/install"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/cobra"
)

var Modules string
var GradlePath string

var IsUEProjectForCurrent bool = false
var logFileName string

// installCmd represents the install command
var installCmd = &cobra.Command{
	Use:   "install",
	Short: "安装iOS nativeSDK",
	Long: `根据gradle文件下载正确的native sdk版本，并解决重复引用冲突
e.g.: onetools install
e.g.: onetools install -p /Users/<USER>/GameProject -m OneSDK,ChatSDK,WMActivitySDK -g /Users/<USER>/GameProject/Onetools/Gradle
e.g.: onetools install -c appid=1001,sdkregion=mainland,env=cb,sdkversion=6.0.0`,
	Run: func(cmd *cobra.Command, args []string) {
		execute(cmd)
	},
}

func init() {
	rootCmd.AddCommand(installCmd)
	installCmd.Flags().StringVarP(&ProjectPath, "project", "p", "", `【可选】项目工程绝对路径
为空时默认为当前命令执行目录`)
	installCmd.Flags().StringVarP(&Modules, "modules", "m", "", `【可选】各业务插件的名称（插件名用英文\",\"隔开）
为空时:
Unity默认扫描Assets/Plugins/iOS/Gradles目录
Unreal Engine默认扫描Plugins目录`)
	installCmd.Flags().StringVarP(&GradlePath, "gradle", "g", "", `【可选】onetools依赖Gradle文件夹的绝对路径
为空时会在onetools命令所在路径查找`)
	installCmd.Flags().StringP("extend", "e", "", `【可选】业务插件扩展字段，一般不需要设置
仅对UE项目有效，防止自动扫描Plugins目录时plugin太多
e.g.: -e custom1Plugin,custom2Plugin`)
	installCmd.Flags().StringP("download_path", "d", "", `【可选】指定SDK下载的路径`)
	installCmd.Flags().StringP("config", "c", "", `【可选】One全球引擎端合并后，请求SDK配置信息参数，多个用","号分割，包含AppID、地区类型、环境类型等，
	e.g.: -c appid=1001,sdkregion=mainland,env=1,sdkversion=6.0.0`)
	installCmd.Flags().StringP("type", "t", "", `【可选】设置SDK配置文件或库文件拉取的类型，如配置文件(config) 或 SDK Lib
	e.g.: -t config、-t lib`)
	installCmd.Flags().Bool("save_log", false, `【可选】是否删除历史日志`)
	installCmd.Flags().String("os", "ios", `【可选】设置平台，目前支持（ios/mac/ps），默认是ios`)
	installCmd.Flags().StringP("dev_url", "u", "", `【可选】调试模式，设置配置文件获取测试地址
	e.g.: -u http://dev-test.sys.wanmei.net`)
	installCmd.Flags().StringVarP(&logFileName, "logFileName", "l", "import_sdk_log.log", `【可选】设置日志文件名称，默认是 "import_sdk_log.log"`)
	installCmd.Flags().BoolP("refresh-dependencies", "U", false, `【可选】强制更新依赖库，由于该指令比较耗时，建议非必要情况下无需更新，默认是不更新`)
	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// installCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// installCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}

func execute(cmd *cobra.Command) {
	if ProjectPath == "" {
		pwdPath, _ := os.Getwd()
		ProjectPath = pwdPath
	}

	if !utility.IsExist(ProjectPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认project路径是否正确", ProjectPath))
	}

	utility.SaveProjectPath(ProjectPath)

	// 设置log日志输出
	generateLogFile(cmd)
	// 设置异常处理
	exceptionHandler()

	// 获取os平台
	osStr, _ := cmd.Flags().GetString("os")
	var platformOS = utility.PlatformOS_IOS
	if osStr == "mac" {
		platformOS = utility.PlatformOS_OSX
	} else if osStr == "ps" {
		platformOS = utility.PlatformOS_PS
	} else if osStr == "ps4" {
		platformOS = utility.PlatformOS_PS4
	} else if osStr == "ps5" {
		platformOS = utility.PlatformOS_PS5
	} else if osStr == "android" {
		platformOS = utility.PlatformOS_Android
	} else if osStr == "windows" {
		platformOS = utility.PlatformOS_Windows
	}
	utility.SetPlatformOS(platformOS)

	// 更新依赖包
	needRefreshDependency, _ := cmd.Flags().GetBool("refresh-dependencies")
	if needRefreshDependency {
		refreshDependency(cmd, platformOS)
		return
	}

	loadConfigParams, _ := cmd.Flags().GetString("config")
	// 分割请求配置文件所需的参数，转成Map
	loadConfigParamArr := strings.Split(loadConfigParams, ",")
	loadConfigMap := make(map[string]string)
	sdkRegion := ""
	for _, pair := range loadConfigParamArr {
		parts := strings.Split(pair, "=")
		if len(parts) == 2 {
			key := parts[0]
			value := parts[1]
			loadConfigMap[key] = value
			if strings.EqualFold(key, "sdkRegion") {
				sdkRegion = value
			}
		}
	}

	installType, _ := cmd.Flags().GetString("type")
	if strings.EqualFold(installType, "config") {
		installSDKConfig(cmd, loadConfigMap, utility.GetPlatformOS())
	} else if strings.EqualFold(installType, "lib") {
		installSDKLib(cmd, sdkRegion)
	} else {
		installSDKConfig(cmd, loadConfigMap, utility.GetPlatformOS())
		installSDKLib(cmd, sdkRegion)
	}
}

func installSDKConfig(cmd *cobra.Command, requestParams map[string]string, platformOS utility.PlatformOS) {
	downloadPath := getDownloadSDKPath(cmd)
	dev_hostUrl, _ := cmd.Flags().GetString("dev_url")
	err := install.FetchProjectConfig(ProjectPath, downloadPath, requestParams, platformOS, dev_hostUrl, "")
	if err != nil {
		log.Printf("未获取到配置文件: 【%s】 \n", err)
		return
	}
	if utility.IsUEProject(ProjectPath) {
		install.FormattingUEIniConfig(ProjectPath, downloadPath, platformOS)
		install.HandleLocalizations(ProjectPath, downloadPath, platformOS)
	}
}

// 安装SDK库文件
func installSDKLib(cmd *cobra.Command, sdkRegion string) {
	platformOSType := utility.GetPlatformOS()
	if platformOSType == utility.PlatformOS_PS4 || platformOSType == utility.PlatformOS_PS5 || platformOSType == utility.PlatformOS_PS {
		// PS4、PS5还没实现从nexus拉取SDK
		return
	}

	// 设置下载路径
	downloadPath := getDownloadSDKPath(cmd)

	if GradlePath == "" {
		// gradlew工具默认的路径
		GradlePath = gradlewToolDefaultPath()
	}

	if !utility.IsExist(GradlePath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认gradle路径是否正确", GradlePath))
	}

	var targetModule string
	if Modules == "" || Modules == "-p" || Modules == "-g" || Modules == "-c" || Modules == "-t" {
		Modules = getProjectDefaultModules(cmd)
		if Modules == "" {
			panic("插件名称必须配置，即[-m 插件1,插件2]")
		}
	}

	if utility.IsUEProject(ProjectPath) {
		extendModule, _ := cmd.Flags().GetString("extend")
		targetModule = utility.UEProjectPluginsFilter(ProjectPath, Modules, extendModule, utility.GetPlatformOS(), false)
	} else if Modules == "Gradles" {
		targetModule = utility.GetSubModules(ProjectPath)
	} else {
		targetModule = Modules
	}

	log.Printf("\n project路径: %s \n modules: %s \n gradle: %s", ProjectPath, targetModule, GradlePath)

	// 获取公共文件，默认取前两个文件的公共位置
	var pluginNames = strings.Split(targetModule, ",")

	var pluginPaths []string
	for _, pluginName := range pluginNames {
		pluginPaths = append(pluginPaths, utility.GetPluginPath(ProjectPath, strings.Trim(pluginName, " "), downloadPath))
	}
	// 打印Java环境
	install.ShowJavaEnvInfo(GradlePath)
	// 删除上一次生成的临时文件
	deleteGradleTmpFiles(downloadPath)
	// 生成build.gradle文件
	install.NewGradle(pluginPaths, downloadPath, false).Execute()
	// 执行gradle下载命令
	install.RunGradlew(downloadPath, GradlePath)
	// 执行gradle查询依赖问题
	install.GetDependencyInfo(downloadPath, GradlePath)
	// 删除脚本执行后生成的临时文件
	deleteGradleTmpFiles(downloadPath)
	// 处理SDK
	isOSXUEProject := (utility.IsUEProject(ProjectPath) && utility.GetPlatformOS() == utility.PlatformOS_OSX)
	if !utility.IsUEProject(ProjectPath) || isOSXUEProject {
		handler := install.NewNativeSDKHandler(downloadPath)
		handler.StartHandle(isOSXUEProject)
	}

	// if utility.IsUEProject(ProjectPath) {
	// 	// UE处理build.cs文件
	// 	install.EditBuildCSFile(pluginPaths, DownloadNativeSDKPath)
	// 	// UE处理UPL文件
	// 	// install.EditUPLFiles(pluginPaths)
	// }
}

func generateLogFile(cmd *cobra.Command) {
	logDirPath := filepath.Join(ProjectPath, "Logs")
	if !utility.IsExist(logDirPath) {
		os.Mkdir(logDirPath, 0777)
	}

	logPath := filepath.Join(logDirPath, logFileName)
	log.Printf("日志文件路径：%s\n", logPath)

	var saveLog, _ = cmd.Flags().GetBool("save_log")
	if utility.IsExist(logPath) && !saveLog {
		os.Remove(logPath)
	}

	logFile, err := os.OpenFile(logPath, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	// 设置存储位置
	log.SetOutput(logFile)
}

func exceptionHandler() {
	defer func() {
		if err := recover(); err != nil {
			log.Println(err)
		}
	}()
}

func gradlewToolDefaultPath() string {
	dir, _ := os.Executable()
	exePath := filepath.Dir(dir)
	return filepath.Join(exePath, "Gradle")
}

func deleteGradleTmpFiles(buildGradlePath string) {
	//删除 build.gradle 文件
	buildGradleFilePath := filepath.Join(buildGradlePath, "build.gradle")
	if utility.IsExist(buildGradleFilePath) {
		os.Remove(buildGradleFilePath)
	}

	//删除.gradle 目录
	buildGradleDirPath := filepath.Join(buildGradlePath, ".gradle")
	if utility.IsExist(buildGradleDirPath) {
		os.RemoveAll(buildGradleDirPath)
	}

	//删除build report目录
	buildReportDirPath := filepath.Join(buildGradlePath, "build")
	if utility.IsExist(buildReportDirPath) {
		os.RemoveAll(buildReportDirPath)
	}
}

func deleteDownloadedNativeSDK(downloadPath string) {
	nativeSDKPath := filepath.Join(downloadPath, utility.GetNativeSDKName())
	if utility.IsExist(nativeSDKPath) {
		utility.RemoveDir(nativeSDKPath)
	}
}

func getProjectDefaultModules(cmd *cobra.Command) string {
	if utility.IsUEProject(ProjectPath) {
		downloadPath := getDownloadSDKPath(cmd)
		defaultModules := utility.UEProjectEnableSDKPlugins(ProjectPath, downloadPath)
		return strings.Join(defaultModules, ",")

	} else {
		return "Gradles"
	}
}

func refreshDependency(cmd *cobra.Command, platformOS utility.PlatformOS) {
	// 设置平台
	utility.SetPlatformOS(platformOS)
	// 设置下载路径
	downloadPath := getDownloadSDKPath(cmd)

	// 设置gradle工具的路径
	if GradlePath == "" {
		// gradlew工具默认的路径
		GradlePath = gradlewToolDefaultPath()
	}

	if !utility.IsExist(GradlePath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认gradle路径是否正确", GradlePath))
	}

	var targetModule string
	if Modules == "" || Modules == "-p" || Modules == "-g" || Modules == "-c" || Modules == "-t" {
		Modules = getProjectDefaultModules(cmd)
		if Modules == "" {
			panic("插件名称必须配置，即[-m 插件1,插件2]")
		}
	}

	if utility.IsUEProject(ProjectPath) {
		extendModule, _ := cmd.Flags().GetString("extend")
		targetModule = utility.UEProjectPluginsFilter(ProjectPath, Modules, extendModule, utility.GetPlatformOS(), true)
	} else if Modules == "Gradles" {
		targetModule = utility.GetSubModules(ProjectPath)
	} else {
		targetModule = Modules
	}

	log.Printf("\n project路径: %s \n modules: %s \n gradle: %s", ProjectPath, targetModule, GradlePath)

	// 获取公共文件，默认取前两个文件的公共位置
	var pluginNames = strings.Split(targetModule, ",")

	// 此处需要对每个子模块单独进行资源更新，防止多个模块引用相同组件的不同版本时，只会更新高版本的资源，不会更新低版本资源的问题
	for _, pluginName := range pluginNames {
		pluginPaths := []string{utility.GetPluginPath(ProjectPath, strings.Trim(pluginName, " "), downloadPath)}
		// 删除上一次生成的临时文件
		deleteGradleTmpFiles(downloadPath)
		// 生成build.gradle文件
		install.NewGradle(pluginPaths, downloadPath, true).Execute()
		// 执行gradle查询依赖问题
		install.RefreshDependency(downloadPath, GradlePath)
		// 删除脚本执行后生成的临时文件
		deleteGradleTmpFiles(downloadPath)
		// 删除已下载的SDK
		deleteDownloadedNativeSDK(downloadPath)
	}
}

func getDownloadSDKPath(cmd *cobra.Command) string {
	// 设置下载路径
	downloadPath, _ := cmd.Flags().GetString("download_path")
	if downloadPath == "" {
		downloadPath = utility.GetDownloadNativeSDKPath(ProjectPath)
	} else if !filepath.IsAbs(downloadPath) {
		downloadPath, _ = filepath.Abs(downloadPath)
	}

	// 下载路径不存在，则创建该路径
	if !utility.IsExist(downloadPath) {
		utility.Mkdir(downloadPath)
	}

	return downloadPath
}
