/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"onetools/cmd/ipa"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/spf13/cobra"
)

var IPAPath string
var OutputPath string
var IconPaths string
var UnrealProjectPath string

// resign command variables
var BundleID string
var ProvisioningProfile string
var Certificate string
var CertificateType string
var DisplayName string
var AppVersion string
var BuildVersion string
var AppIconPath string
var LaunchScreen string
var ForceSwiftUpdate bool
var EntitlementsPath string
var KeepTempDir bool
var AdditionalPlistPath string
var RemovePlistKeys string
var DeleteFilePaths string
var AddFilesPath string
var AppDevConfig string

// build command variables
var Workspace string
var Project string
var Scheme string
var Configuration string
var XcodeBuildArgs string

// log output variables
var LogOutputMode string

// profiles command variables
var ShowProfiles bool
var ProfilesOutputJSON bool

// match-cert command variables
var ProvisionPath string
var ShowAllMatches bool

// checkSystemSupport 检查系统环境是否支持ipa命令
func checkSystemSupport() error {
	if runtime.GOOS != "darwin" {
		return fmt.Errorf("ipa命令仅支持macOS系统，当前系统: %s", runtime.GOOS)
	}
	return nil
}

// ipaCmd represents the ipa command
var ipaCmd = &cobra.Command{
	Use:   "ipa",
	Short: "处理iOS应用包文件",
	Long: `ipa命令用于处理iOS应用包文件，支持以下操作：
  build         构建iOS项目生成ipa文件
  unzip         解压ipa文件
  swiftSupport  修复ipa中SwiftSupport
  alt_icons     生成iOS应用的备用图标
  resign        重新签名ipa文件
  rpath         验证和修复ipa中的rpath配置
  list          列出ipa文件中的所有文件路径
  match-cert    根据mobileprovision文件匹配本地证书

使用 --profiles 标志可以列出系统中的iOS配置文件信息

示例：
  onetools ipa build --project-path /path/to/project
  onetools ipa unzip -p /path/to/app.ipa -o /path/to/output
  onetools ipa swiftSupport -p /path/to/app.ipa
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --project /path/to/UnrealProject
  onetools ipa resign -p /path/to/app.ipa --bundle-id com.example.app --provision /path/to/profile.mobileprovision
  onetools ipa rpath -p /path/to/app.ipa --fix
  onetools ipa list -p /path/to/app.ipa
  onetools ipa match-cert -p /path/to/profile.mobileprovision
  onetools ipa --profiles
  onetools ipa --profiles --json`,
	Run: ipaExecute,
}

// unzip command
var unzip = &cobra.Command{
	Use:   "unzip",
	Short: "解压ipa文件",
	Long:  `解压指定的ipa文件到目标目录`,
	Run:   unzipExecute,
}

// swiftSupport command
var swiftSupport = &cobra.Command{
	Use:   "swiftSupport",
	Short: "修复ipa中SwiftSupport",
	Long:  `修复ipa文件中的SwiftSupport库文件`,
	Run:   fixSwiftSupportExecute,
}

// alt_icons command
var alternateIcons = &cobra.Command{
	Use:   "alt_icons",
	Short: "生成iOS应用的备用图标",
	Long: `生成iOS应用的备用图标，支持Unity和Unreal Engine项目集成
要求图标尺寸为1024x1024像素，支持PNG和JPEG格式

示例：
  # 基本用法
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png,/path/to/icon3.png
  
  # 指定输出目录
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --output /path/to/output
  
  # 集成到 Unity 项目
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --project /path/to/UnityProject
  
  # 集成到 Unreal 项目
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --project /path/to/UnrealProject`,
	Run: alternateIconsExecute,
}

// resign command
var resign = &cobra.Command{
	Use:   "resign",
	Short: "重新签名ipa文件",
	Long: `重新签名ipa文件，支持替换Bundle ID、mobileprovision、证书等
支持替换显示名称、版本号、应用图标和启动图片
包含动态库时会自动重签动态库，Distribution包含Swift库时支持强制更新SwiftSupport
支持自定义entitlements.plist文件，如不指定则从mobileprovision中提取
支持合并附加的plist文件到Info.plist中
支持删除Info.plist中指定的最外层key
支持删除.app目录中指定的文件或文件夹
支持通过证书类型自动选择合适的开发或发布证书

示例：
  # 基本重签名
  onetools ipa resign -p /path/to/app.ipa --bundle-id com.example.newapp

  # 完整重签名
  onetools ipa resign -p /path/to/app.ipa \
    --bundle-id com.example.newapp \
    --provision /path/to/profile.mobileprovision \
    --cert "iPhone Developer: Your Name (XXXXXXXXXX)" \
    --display-name "New App Name" \
    --app-version "2.0.0" \
    --build-version "200" \
    --app-icon /path/to/icon.png \
    --launch-screen /path/to/launch.png \
    --entitlements /path/to/entitlements.plist \
    --additional-plist /path/to/additional.plist \
    --remove-plist-keys "key1,key2,key3" \
    --delete-files "Frameworks/unused.framework,resources/large_video.mp4" \
    --add-files-path /path/to/additional/files \
    --force-swift-update \
	--app-dev-config "appid=1000000,env=prod" \
    -o /path/to/outputdir

  # 使用证书类型自动选择证书
  onetools ipa resign -p /path/to/app.ipa \
    --bundle-id com.example.newapp \
    --cert-type dev \
    -o /path/to/output.ipa`,
	Run: resignExecute,
}

// rpath command
var rpathCmd = &cobra.Command{
	Use:   "rpath",
	Short: "验证和修复ipa中的rpath配置",
	Long: `验证和修复ipa文件中的rpath配置，确保动态库能正确加载
主要功能：
1. 打印ipa主要加载的动态库
2. 打印主程序中LC_RPATH的路径
3. 扫描动态库的位置，并检查自定义的动态库的rpath是否都正确
4. 如果动态库没被正确加载，则需要添加rpath，并确保主可执行文件的Load command中能正确加载
5. 针对Unity项目，会自动识别UnityFramework作为主可执行文件

示例：
  # 验证rpath配置
  onetools ipa rpath -p /path/to/app.ipa`,
	Run: rpathExecute,
}

// list command
var listCmd = &cobra.Command{
	Use:   "list",
	Short: "列出ipa文件中的所有文件路径",
	Long: `解压ipa文件到临时目录，列出其中包含的所有文件和目录路径，然后清理临时目录
主要功能：
1. 将ipa文件解压到临时目录
2. 遍历并打印所有文件和目录的路径
3. 显示文件和目录的统计信息
4. 自动清理临时目录

示例：
  # 列出ipa文件中的所有文件路径
  onetools ipa list -p /path/to/app.ipa`,
	Run: listExecute,
}

// match-cert command
var matchCertCmd = &cobra.Command{
	Use:   "match-cert",
	Short: "根据mobileprovision文件匹配本地证书",
	Long: `根据指定的mobileprovision文件，自动匹配本地钥匙串中对应的代码签名证书
主要功能：
1. 解析mobileprovision文件中的开发者证书
2. 获取本地钥匙串中的代码签名身份
3. 通过SHA1指纹匹配证书，返回匹配的证书名称
4. 支持显示所有匹配结果或仅显示第一个匹配结果

示例：
  # 匹配第一个可用的证书
  onetools ipa match-cert -p /path/to/profile.mobileprovision
  
  # 显示所有匹配的证书
  onetools ipa match-cert -p /path/to/profile.mobileprovision --all`,
	Run: matchCertExecute,
}

// build command
var buildCmd = &cobra.Command{
	Use:   "build",
	Short: "构建iOS项目生成ipa文件",
	Long: `使用xcodebuild构建iOS项目
主要功能：
1. 自动检测workspace或project文件及scheme
2. 自动根据BundleId和目标类型(dev、adhoc、dis)获取证书和描述文件
3. 自动添加-ObjC Linker Flag
4. 支持从dev后台拉取配置，自动处理URLScheme、权限描述及Entitlements
5. 执行xcodebuild构建并生成ipa文件

示例：
  # 基本构建（自动检测workspace/project和scheme）
  onetools ipa build --project-path /path/to/project --bundle-id com.your.app  --cert-type dev --configuration Release
  
  # 指定workspace和scheme
  onetools ipa build --project-path /path/to/project --workspace MyApp.xcworkspace --scheme MyApp --provision yourAppprovision --configuration Release
  
  # 指定project和scheme
  onetools ipa build --project-path /path/to/project --project MyApp.xcodeproj --scheme MyApp
  
  # 使用额外的xcodebuild参数
  onetools ipa build --project-path /path/to/project --xcodebuild-args "VALID_ARCHS=x86_64 -UseModernBuildSystem=YES"`,
	Run: buildExecute,
}

func init() {
	rootCmd.AddCommand(ipaCmd)

	// Add subcommands
	ipaCmd.AddCommand(unzip)
	ipaCmd.AddCommand(swiftSupport)
	ipaCmd.AddCommand(alternateIcons)
	ipaCmd.AddCommand(resign)
	ipaCmd.AddCommand(rpathCmd)
	ipaCmd.AddCommand(listCmd)
	ipaCmd.AddCommand(matchCertCmd)
	ipaCmd.AddCommand(buildCmd)

	// Add flags to unzip command
	unzip.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)
	unzip.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】执行完成后，输出目标文件路径`)

	// Add flags to swiftSupport command
	swiftSupport.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)
	swiftSupport.Flags().BoolP("forceUpdate", "f", false, `【可选】强制更新SwiftSupport文件`)

	// Add flags to alternate_icons command
	alternateIcons.Flags().StringVar(&IconPaths, "icons", "", `【必传】图标文件路径，多个文件用逗号分隔`)
	alternateIcons.Flags().StringVar(&UnrealProjectPath, "project", "", `【可选】Unreal项目路径`)
	alternateIcons.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】输出目录路径`)

	// Add flags to resign command
	resign.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)
	resign.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】输出ipa文件路径`)
	resign.Flags().StringVar(&BundleID, "bundle-id", "", `【可选】新的Bundle ID`)
	resign.Flags().StringVar(&ProvisioningProfile, "provision", "", `【可选】ProvisioningProfile文件名或mobileprovision文件路径，未设置时根据bundleId和cert-type自动搜索mobileprovision`)
	resign.Flags().StringVar(&Certificate, "cert", "", `【可选】证书名称，如"Apple Developer: Your Name (XXXXXXXXXX)"或"Apple Distribution，未设置时根据provision自动搜索"`)
	resign.Flags().StringVar(&CertificateType, "cert-type", "", `【可选】证书类型，可选值：dev、adhoc、dis、ent。当未指定具体provision时，将根据此类型自动选择合适的provision和证书`)
	resign.Flags().StringVar(&DisplayName, "display-name", "", `【可选】应用显示名称`)
	resign.Flags().StringVar(&AppVersion, "app-version", "", `【可选】应用版本号`)
	resign.Flags().StringVar(&BuildVersion, "build-version", "", `【可选】构建版本号`)
	resign.Flags().StringVar(&AppIconPath, "app-icon", "", `【可选】应用图标文件路径,支持传1024x1024的png图，也可以传Assets.xcassets文件，适合Assets.xcassets里还包含非AppIcon资源的情况。`)
	resign.Flags().StringVar(&LaunchScreen, "launch-screen", "", `【可选】启动图文件路径，支持传入一张图片，用于替换闪屏启动图`)
	resign.Flags().BoolVar(&ForceSwiftUpdate, "force-swift-update", false, `【可选】强制更新SwiftSupport，默认会根据原包中是否包含SwiftSupport进行自动处理`)
	resign.Flags().StringVar(&EntitlementsPath, "entitlements", "", `【可选】自定义 entitlements.plist 文件路径，默认会解析IPA包，自动处理entitlements`)
	resign.Flags().BoolVar(&KeepTempDir, "keep-temp-dir", false, `【可选】保留临时文件目录，用于调试，默认为false（处理完成后删除临时文件）`)
	resign.Flags().StringVar(&AdditionalPlistPath, "additional-plist", "", `【可选】附加的 plist 文件路径，用于添加、替换复杂结构的Key，如CFBundleURLTypes，将与当前IPA中Info.plist顶层key进行合并或替换处理`)
	resign.Flags().StringVar(&RemovePlistKeys, "remove-plist-keys", "", `【可选】要删除的 Info.plist 中顶层 key，多个key用逗号分隔`)
	resign.Flags().StringVarP(&AddFilesPath, "add-files-path", "a", "", `【可选】要添加到.app目录的文件夹路径，该文件夹内的文件结构对应IPA解压后.app目录的根目录结构。可用于替换Launcher图、配置文件等 (注：当添加Resources目录后会导致重签失败)`)
	resign.Flags().StringVar(&DeleteFilePaths, "delete-files", "", `【可选】要从.app目录中删除的文件或文件夹路径，多个路径用逗号分隔，路径以.app根目录为基准`)
	resign.Flags().StringVar(&AppDevConfig, "app-dev-config", "", `【可选】请求dev后台App对应的配置文件所需参数，将请求到的配置文件替换到重签后的ipa中。格式：appid=1000000,env=0，可选参数host=xxx`)
	resign.Flags().StringVar(&LogOutputMode, "log-output", "console", `【可选】日志输出模式，可选值：console（控制台输出）、file（输出到文件），默认console`)

	// Add flags to rpath command
	rpathCmd.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)

	// Add flags to list command
	listCmd.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)

	// Add flags to match-cert command
	matchCertCmd.Flags().StringVarP(&ProvisionPath, "path", "p", "", `【必传】mobileprovision文件路径`)
	matchCertCmd.Flags().BoolVar(&ShowAllMatches, "all", false, `【可选】显示所有匹配的证书，默认只显示第一个匹配的证书`)

	// Add flags to build command
	buildCmd.Flags().StringVarP(&ProjectPath, "path", "p", "", `【必传】Xcode项目工程根路径`)
	buildCmd.Flags().StringVar(&Workspace, "workspace", "", `【可选】workspace文件名（如MyApp.xcworkspace）`)
	buildCmd.Flags().StringVar(&Project, "project", "", `【可选】project文件名（如MyApp.xcodeproj）`)
	buildCmd.Flags().StringVar(&Scheme, "scheme", "", `【可选】构建scheme，如果不指定则自动获取`)
	buildCmd.Flags().StringVarP(&OutputPath, "output-dir", "o", "", `【可选】输出Build目录路径`)
	buildCmd.Flags().StringVar(&BundleID, "bundle-id", "", `【可选】Bundle ID，没传时默认读取工程设置的`)
	buildCmd.Flags().StringVar(&ProvisioningProfile, "provision", "", `【可选】ProvisioningProfile文件名或mobileprovision文件路径，未设置时根据bundleId和cert-type自动搜索mobileprovision`)
	buildCmd.Flags().StringVar(&CertificateType, "cert-type", "", `【可选】证书类型，可选值：dev、adhoc、dis、ent。当未指定具体provision时，将根据此类型自动选择合适的provision和证书`)
	buildCmd.Flags().StringVar(&Configuration, "configuration", "Release", `【可选】构建配置，默认为Release`)
	buildCmd.Flags().StringVar(&XcodeBuildArgs, "xcodebuild-args", "", `【可选】追加到xcodebuild archive命令的额外参数，如：VALID_ARCHS=x86_64 -UseModernBuildSystem=YES`)
	buildCmd.Flags().StringVarP(&AddFilesPath, "add-files-path", "a", "", `【可选】要添加到.app目录的文件夹路径，该文件夹内的文件结构对应IPA解压后.app目录的根目录结构。可用于替换Launcher图、配置文件等 (注：当添加Resources目录后会导致重签失败)`)
	buildCmd.Flags().StringVar(&AppDevConfig, "app-dev-config", "", `【可选】请求dev后台App对应的配置文件所需参数，将请求到的配置文件替换到重签后的ipa中。格式：appid=1000000,env=0，可选参数host=xxx`)
	buildCmd.Flags().StringVar(&LogOutputMode, "log-output", "console", `【可选】日志输出模式，可选值：console（控制台输出）、file（输出到文件），默认console`)

	// Add flags to main ipa command
	ipaCmd.Flags().BoolVar(&ShowProfiles, "profiles", false, `【可选】列出iOS配置文件信息`)
	ipaCmd.Flags().BoolVar(&ProfilesOutputJSON, "json", false, `【可选】以JSON格式输出配置文件信息（需要与--profiles一起使用）`)
}

func unzipExecute(cmd *cobra.Command, args []string) {
	if OutputPath == "" {
		pwdPath, _ := os.Getwd()
		OutputPath = pwdPath
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	// 确保OutputPath是绝对路径
	absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
	OutputPath = absOutputPath

	// 解压IPA文件
	fmt.Println("开始解压IPA文件...")
	err := ipa.UnzipIPAExe(IPAPath, OutputPath)
	// 输出err
	if err != nil {
		panic(fmt.Sprintf("解压IPA文件失败: %v", err))
	} else {
		fmt.Printf("解压完成，路径：%s\n", OutputPath)
	}
}

// 修改ipa包中的SwiftSupport文件
func fixSwiftSupportExecute(cmd *cobra.Command, args []string) {
	// 检查系统环境
	if err := checkSystemSupport(); err != nil {
		fmt.Printf("错误：%v\n", err)
		os.Exit(1)
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	forceUpdate, _ := cmd.Flags().GetBool("forceUpdate")
	ipa.FixSwiftSupportExe(IPAPath, forceUpdate)
}

// 生成iOS应用的备用图标
func alternateIconsExecute(cmd *cobra.Command, args []string) {
	// 检查系统环境
	if err := checkSystemSupport(); err != nil {
		fmt.Printf("错误：%v\n", err)
		os.Exit(1)
	}

	if IconPaths == "" {
		fmt.Println("错误：必须指定图标文件路径")
		fmt.Println("使用方法：onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png")
		return
	}

	// Parse icon paths
	var iconPathsList []string
	if strings.Contains(IconPaths, ",") {
		iconPathsList = strings.Split(IconPaths, ",")
	} else {
		iconPathsList = []string{IconPaths}
	}

	// Trim whitespace and ensure absolute paths
	for i := range iconPathsList {
		iconPathsList[i] = strings.TrimSpace(iconPathsList[i])
		// 确保图标路径是绝对路径
		absPath, _ := utility.EnsureAbsolutePath(iconPathsList[i])
		iconPathsList[i] = absPath
	}

	if len(iconPathsList) == 0 {
		fmt.Println("错误：至少需要指定一个图标文件")
		return
	}

	// 确保项目路径是绝对路径
	if UnrealProjectPath != "" {
		absProjectPath, _ := utility.EnsureAbsolutePath(UnrealProjectPath)
		UnrealProjectPath = absProjectPath
	}

	// 确保输出路径是绝对路径
	if OutputPath != "" {
		absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
		OutputPath = absOutputPath
	}

	// Execute the alternate icons generation
	err := ipa.GenerateAlternateIconsExe(iconPathsList, UnrealProjectPath, OutputPath)
	if err != nil {
		fmt.Printf("生成备用图标失败: %v\n", err)
		os.Exit(1)
	}
}

// 重新签名ipa文件
func resignExecute(cmd *cobra.Command, args []string) {
	// 检查系统环境
	if err := checkSystemSupport(); err != nil {
		fmt.Printf("错误：%v\n", err)
		os.Exit(1)
	}

	if IPAPath == "" || !strings.HasSuffix(IPAPath, ".ipa") {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:    -1,
				Message: fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确，只接受.ipa文件", IPAPath),
				LogPath: "",
			}
			fmt.Print(result.ToJSON())
		} else {
			fmt.Printf("错误：【%s】路径不存在，请确认IPA路径是否正确，只接受.ipa文件\n", IPAPath)
		}
		return
	}

	// 验证证书类型参数
	if CertificateType != "" {
		certType := strings.ToLower(CertificateType)
		if certType != "dev" && certType != "adhoc" && certType != "dis" && certType != "ent" {
			if LogOutputMode == "file" {
				result := &ipa.JsonModelResult{
					Code:    -1,
					Message: fmt.Sprintf("错误：不支持的证书类型 '%s'，支持的类型：dev、adhoc、dis、ent", CertificateType),
					LogPath: "",
				}
				fmt.Print(result.ToJSON())
			} else {
				fmt.Printf("错误：不支持的证书类型 '%s'，支持的类型：dev、adhoc、dis、ent\n", CertificateType)
			}
			return
		}
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	// 初始化日志管理器
	var logFilePath string
	if LogOutputMode == "file" {
		// 生成默认日志文件路径
		ipaDir := filepath.Dir(IPAPath)
		ipaName := strings.TrimSuffix(filepath.Base(IPAPath), ".ipa")
		timestamp := time.Now().Format("20060102_150405.000")
		logFilePath = filepath.Join(ipaDir, fmt.Sprintf("%s_resign_%s.txt", ipaName, timestamp))

		// 确保日志文件路径是绝对路径
		absLogFilePath, _ := utility.EnsureAbsolutePath(logFilePath)
		logFilePath = absLogFilePath

		if err := utility.InitLogger(utility.LoggerModeFile, logFilePath); err != nil {
			fmt.Printf("初始化日志文件失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	} else {
		if err := utility.InitLogger(utility.LoggerModeConsole, ""); err != nil {
			fmt.Printf("初始化控制台日志失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	}

	// 检查IPA文件是否存在
	if !utility.IsExist(IPAPath) {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:    -1,
				Message: fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath),
				LogPath: logFilePath,
			}
			fmt.Print(result.ToJSON())
		} else {
			fmt.Printf("错误：【%s】路径不存在，请确认IPA路径是否正确\n", IPAPath)
		}
		return
	}

	// 确保其他路径参数是绝对路径
	if OutputPath != "" {
		absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
		OutputPath = utility.GetDir(absOutputPath)
	}

	if ProvisioningProfile != "" && strings.HasSuffix(ProvisioningProfile, ".mobileprovision") {
		absProvisioningProfile, _ := utility.EnsureAbsolutePath(ProvisioningProfile)
		ProvisioningProfile = absProvisioningProfile
	}

	if AppIconPath != "" {
		absAppIconPath, _ := utility.EnsureAbsolutePath(AppIconPath)
		AppIconPath = absAppIconPath
	}

	if EntitlementsPath != "" {
		absEntitlementsPath, _ := utility.EnsureAbsolutePath(EntitlementsPath)
		EntitlementsPath = absEntitlementsPath
	}

	// 确保附加的plist文件路径是绝对路径
	if AdditionalPlistPath != "" {
		absAdditionalPlistPath, _ := utility.EnsureAbsolutePath(AdditionalPlistPath)
		AdditionalPlistPath = absAdditionalPlistPath
	}

	if AddFilesPath != "" {
		absAddFilesPath, _ := utility.EnsureAbsolutePath(AddFilesPath)
		AddFilesPath = absAddFilesPath
	}

	// 创建重签名配置
	config := &ipa.ResignConfig{
		IPAPath:             IPAPath,
		OutputPath:          OutputPath,
		BundleID:            BundleID,
		ProvisioningProfile: ProvisioningProfile,
		Certificate:         Certificate,
		CertificateType:     CertificateType,
		DisplayName:         DisplayName,
		AppVersion:          AppVersion,
		BuildVersion:        BuildVersion,
		AppIconPath:         AppIconPath,
		LaunchScreen:        LaunchScreen,
		ForceSwiftUpdate:    ForceSwiftUpdate,
		EntitlementsPath:    EntitlementsPath,
		KeepTempDir:         KeepTempDir,
		AdditionalPlistPath: AdditionalPlistPath,
		RemovePlistKeys:     RemovePlistKeys,
		DeleteFilePaths:     DeleteFilePaths,
		AddFilesPath:        AddFilesPath,
		AppDevConfig:        AppDevConfig,
		Platform:            utility.PlatformOS_IOS,
	}

	// 执行重签名
	outputPath, err := ipa.ResignIPAExe(config)

	// 如果是文件输出模式，输出JSON结果
	if LogOutputMode == "file" {
		if err != nil {
			retuslt := ipa.JsonModelResult{
				Code:    -1,
				Message: err.Error(),
				LogPath: logFilePath,
			}
			fmt.Print(retuslt.ToJSON())
		} else {
			retuslt := ipa.JsonModelResult{
				Code:       0,
				Message:    "success",
				LogPath:    logFilePath,
				OutputPath: outputPath,
			}
			fmt.Print(retuslt.ToJSON())
		}

	} else {
		// 控制台模式，输出传统格式
		if err != nil {
			fmt.Printf("重签名失败: %s\n", err.Error())
			os.Exit(1)
		}
	}
}

// 验证和修复rpath配置
func rpathExecute(cmd *cobra.Command, args []string) {
	// 检查系统环境
	if err := checkSystemSupport(); err != nil {
		fmt.Printf("错误：%v\n", err)
		os.Exit(1)
	}

	if IPAPath == "" || !strings.HasSuffix(IPAPath, ".ipa") {
		fmt.Println("错误：必须指定ipa文件路径")
		fmt.Println("使用方法：onetools ipa rpath -p /path/to/app.ipa")
		return
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	// 执行rpath验证和修复
	err := ipa.VerifyAndFixRpathExe(IPAPath, utility.PlatformOS_IOS)
	if err != nil {
		fmt.Printf("rpath验证失败: %v\n", err)
		os.Exit(1)
	}
}

// 列出ipa文件中的所有文件路径
func listExecute(cmd *cobra.Command, args []string) {
	if IPAPath == "" {
		fmt.Println("错误：必须指定ipa文件路径")
		fmt.Println("使用方法：onetools ipa list -p /path/to/app.ipa")
		return
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	// 执行文件列表功能
	err := ipa.ListIPAFilesExe(IPAPath)
	if err != nil {
		fmt.Printf("列出文件失败: %v\n", err)
		os.Exit(1)
	}
}

// 匹配证书
func matchCertExecute(cmd *cobra.Command, args []string) {
	// 检查系统环境
	if err := checkSystemSupport(); err != nil {
		fmt.Printf("错误：%v\n", err)
		os.Exit(1)
	}

	if ProvisionPath == "" {
		fmt.Println("错误：必须指定mobileprovision文件路径")
		fmt.Println("使用方法：onetools ipa match-cert -p /path/to/profile.mobileprovision")
		return
	}

	// 确保ProvisionPath是绝对路径
	absProvisionPath, _ := utility.EnsureAbsolutePath(ProvisionPath)
	ProvisionPath = absProvisionPath

	if !utility.IsExist(ProvisionPath) {
		fmt.Printf("错误：【%s】路径不存在，请确认mobileprovision文件路径是否正确\n", ProvisionPath)
		return
	}

	if ShowAllMatches {
		// 显示所有匹配的证书
		results, err := ipa.GetAllMatchingCertificates(ProvisionPath)
		if err != nil {
			fmt.Printf("匹配证书失败: %v\n", err)
			os.Exit(1)
		}

		if len(results) == 0 {
			fmt.Println("未找到任何证书")
			return
		}

		fmt.Printf("找到 %d 个证书：\n", len(results))
		for i, result := range results {
			fmt.Printf("\n证书 %d:\n", i+1)
			if result.Found {
				fmt.Printf("  ✅ 匹配成功\n")
				fmt.Printf("  证书名称: %s\n", result.IdentityName)
				fmt.Printf("  SHA1指纹: %s\n", result.SHA1)
			} else {
				fmt.Printf("  ❌ 匹配失败\n")
				if result.SHA1 != "" {
					fmt.Printf("  SHA1指纹: %s\n", result.SHA1)
				}
				fmt.Printf("  错误信息: %s\n", result.Error)
			}
		}
	} else {
		// 只显示第一个匹配的证书
		result, err := ipa.MatchCertificateFromProfile(ProvisionPath)
		if err != nil {
			fmt.Printf("匹配证书失败: %v\n", err)
			os.Exit(1)
		}

		if result.Found {
			fmt.Printf("✅ 找到匹配的证书: %s\n", result.IdentityName)
			// fmt.Printf("SHA1指纹: %s\n", result.SHA1)
		} else {
			fmt.Printf("❌ 未找到匹配的证书: %s\n", result.Error)
			os.Exit(1)
		}
	}
}

// 构建iOS项目
func buildExecute(cmd *cobra.Command, args []string) {
	// 检查系统环境
	if err := checkSystemSupport(); err != nil {
		fmt.Printf("错误：%v\n", err)
		os.Exit(1)
	}

	if ProjectPath == "" {
		fmt.Println("错误：必须指定项目路径")
		fmt.Println("使用方法：onetools ipa build --project-path /path/to/project")
		return
	}

	// 确保ProjectPath是绝对路径
	absProjectPath, _ := utility.EnsureAbsolutePath(ProjectPath)
	ProjectPath = absProjectPath

	// 初始化日志管理器
	var logFilePath string
	if LogOutputMode == "file" {
		// 生成默认日志文件路径
		timestamp := time.Now().Format("20060102_150405.000")
		logFilePath = filepath.Join(ProjectPath, fmt.Sprintf("build_%s.txt", timestamp))

		// 确保日志文件路径是绝对路径
		absLogFilePath, _ := utility.EnsureAbsolutePath(logFilePath)
		logFilePath = absLogFilePath

		if err := utility.InitLogger(utility.LoggerModeFile, logFilePath); err != nil {
			fmt.Printf("初始化日志文件失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	} else {
		if err := utility.InitLogger(utility.LoggerModeConsole, ""); err != nil {
			fmt.Printf("初始化控制台日志失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	}

	// 验证证书类型参数
	if CertificateType != "" {
		certType := strings.ToLower(CertificateType)
		if certType != "dev" && certType != "adhoc" && certType != "dis" && certType != "ent" {
			if LogOutputMode == "file" {
				result := &ipa.JsonModelResult{
					Code:    -1,
					Message: fmt.Sprintf("错误：不支持的证书类型 '%s'，支持的类型：dev、adhoc、dis、ent", CertificateType),
					LogPath: "",
				}
				fmt.Print(result.ToJSON())
			} else {
				fmt.Printf("错误：不支持的证书类型 '%s'，支持的类型：dev、adhoc、dis、ent\n", CertificateType)
			}
			return
		}
	}

	// 创建构建配置
	buildConfig := &ipa.BuildConfig{
		ProjectPath:         ProjectPath,
		Workspace:           Workspace,
		Project:             Project,
		Scheme:              Scheme,
		Configuration:       Configuration,
		OutputPath:          OutputPath,
		BundleID:            BundleID,
		ProvisioningProfile: ProvisioningProfile,
		CertificateType:     CertificateType,
		Platform:            utility.PlatformOS_IOS,
		XcodeBuildArgs:      XcodeBuildArgs,
		AppDevConfig:        AppDevConfig,
		AddFilesPath:        AddFilesPath,
	}

	// 执行构建
	output, err := ipa.BuildIPAExe(buildConfig)
	if err != nil {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:       -1,
				Message:    err.Error(),
				LogPath:    logFilePath,
				OutputPath: output,
			}
			fmt.Print(result.ToJSON())
		} else {
			utility.LogPrintError(fmt.Sprintf("构建失败: %v", err))
		}
		os.Exit(1)
	} else {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:       0,
				Message:    "构建完成",
				LogPath:    logFilePath,
				OutputPath: output,
			}
			fmt.Print(result.ToJSON())
		} else {
			utility.LogPrintSuccess("构建完成")
		}
	}
}

// ipa 主命令执行函数
func ipaExecute(cmd *cobra.Command, args []string) {
	if ShowProfiles {
		// 检查系统环境
		if err := checkSystemSupport(); err != nil {
			fmt.Printf("错误：%v\n", err)
			os.Exit(1)
		}

		// 执行配置文件列表功能
		err := ipa.ListProvisioningProfiles(ProfilesOutputJSON, utility.PlatformOS_IOS)
		if err != nil {
			fmt.Printf("获取配置文件信息失败: %v\n", err)
			os.Exit(1)
		}
		return
	}

	// 如果没有使用 --profiles，显示帮助信息
	cmd.Help()
}
