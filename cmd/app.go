/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"onetools/cmd/ipa"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/cobra"
)

var AppPath string

// ipaCmd represents the ipa command
var appCmd = &cobra.Command{
	Use:   "app",
	Short: "处理macOS应用包文件",
	Long: `app命令用于处理macOS应用包文件，支持以下操作：
  resign        重新签名ipa文件
  rpath         验证和修复ipa中的rpath配置
  match-cert    根据mobileprovision文件匹配本地证书

使用 --profiles 标志可以列出系统中的iOS配置文件信息

示例：
  onetools app resign -p /path/to/app.ipa --bundle-id com.example.app --provision /path/to/profile.mobileprovision
  onetools app rpath -p /path/to/app.ipa
  onetools app match-cert -p /path/to/profile.mobileprovision
  onetools app --profiles
  onetools app --profiles --json`,
	Run: appExecute,
}

// resign command
var resignApp = &cobra.Command{
	Use:   "resign",
	Short: "重新签名app文件",
	Long: `重新签名ipa文件，支持替换Bundle ID、mobileprovision、证书等
支持替换显示名称、版本号、应用图标和启动图片
支持自定义entitlements.plist文件，如不指定则从mobileprovision中提取
支持合并附加的plist文件到Info.plist中
支持删除Info.plist中指定的最外层key
支持删除.app目录中指定的文件或文件夹
支持通过证书类型自动选择合适的开发或发布证书

示例：
  # 基本重签名
  onetools app resign -p /path/to/example.app.zip --bundle-id com.example.newapp

  # 完整重签名
  onetools app resign -p /path/to/example.zip \
    --bundle-id com.example.newapp \
    --provision /path/to/AppStore_Dis.provisionprofile \
    --cert "iPhone Developer: Your Name (XXXXXXXXXX)" \
    --display-name "New App Name" \
    --app-version "2.0.0" \
    --build-version "200" \
    --app-icon /path/to/icon.png \
    --entitlements /path/to/entitlements.plist \
    --additional-plist /path/to/additional.plist \
    --remove-plist-keys "key1,key2,key3" \
    --delete-files "Frameworks/unused.framework,resources/large_video.mp4" \
    --add-files-path /path/to/additional/files \
    --force-swift-update \
	--app-dev-config "appid=1000000,env=prod" \
    -o /path/to/outputdir

  # 使用证书类型自动选择证书
  onetools app resign -p /path/to/app.zip \
    --bundle-id com.example.newapp \
    --cert-type dev \
    -o /path/to/output.zip`,
	Run: resignAppExecute,
}

// rpath command
var rpathAppCmd = &cobra.Command{
	Use:   "rpath",
	Short: "验证和修复app包中的rpath配置",
	Long: `验证和修复app文件中的rpath配置，确保动态库能正确加载
主要功能：
1. 打印app主要加载的动态库
2. 打印主程序中LC_RPATH的路径
3. 扫描动态库的位置，并检查自定义的动态库的rpath是否都正确
4. 如果动态库没被正确加载，则需要添加rpath，并确保主可执行文件的Load command中能正确加载
5. 针对Unity项目，会自动识别UnityFramework作为主可执行文件

示例：
  # 验证rpath配置
  onetools app rpath -p /path/to/app.zip`,
	Run: rpathAppExecute,
}

// build command
var buildAppCmd = &cobra.Command{
	Use:   "build",
	Short: "构建macOS项目生成app文件",
	Long: `使用xcodebuild构建macOS项目
主要功能：
1. 自动检测workspace或project文件及scheme
2. 自动根据BundleId和目标类型(dev、adhoc、dis)获取证书和描述文件
3. 自动添加-ObjC Linker Flag
4. 执行xcodebuild构建并生成.app或.pkg文件

示例：
  # 基本构建（自动检测workspace/project和scheme）
  onetools app build --project-path /path/to/project --bundle-id com.your.app --cert-type dev --configuration Release
  
  # 指定workspace和scheme
  onetools app build --project-path /path/to/project --workspace MyApp.xcworkspace --scheme MyApp --provision yourAppprovision --configuration Release
  
  # 指定project和scheme
  onetools app build --project-path /path/to/project --project MyApp.xcodeproj --scheme MyApp`,
	Run: buildAppExecute,
}

func init() {
	rootCmd.AddCommand(appCmd)

	// // Add subcommands
	appCmd.AddCommand(resignApp)
	appCmd.AddCommand(rpathAppCmd)
	appCmd.AddCommand(matchCertCmd)
	appCmd.AddCommand(buildAppCmd)

	// Add flags to resign command
	resignApp.Flags().StringVarP(&AppPath, "path", "p", "", `【必传】支持.zip和.pkg 文件路径`)
	resignApp.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】输出.zip文件路径`)
	resignApp.Flags().StringVar(&BundleID, "bundle-id", "", `【可选】新的Bundle ID`)
	resignApp.Flags().StringVar(&ProvisioningProfile, "provision", "", `【可选】provisionprofile文件路径或文件名，未设置时根据bundleId和cert-type自动搜索provisionprofile`)
	resignApp.Flags().StringVar(&Certificate, "cert", "", `【可选】证书名称，如"Apple Developer: Your Name (XXXXXXXXXX)"或"Apple Distribution，未设置时根据provision自动搜索"`)
	resignApp.Flags().StringVar(&CertificateType, "cert-type", "", `【可选】证书类型，可选值：dev(开发)、dis(提交到AppStore)、did(侧载发布) 。当未指定具体provision时，将根据此类型自动选择合适的provision和证书`)
	resignApp.Flags().StringVar(&DisplayName, "display-name", "", `【可选】应用显示名称`)
	resignApp.Flags().StringVar(&AppVersion, "app-version", "", `【可选】应用版本号`)
	resignApp.Flags().StringVar(&BuildVersion, "build-version", "", `【可选】构建版本号`)
	resignApp.Flags().StringVar(&AppIconPath, "app-icon", "", `【可选】应用图标文件路径,支持传1024x1024的png图，也可以传Assets.xcassets文件，适合Assets.xcassets里还包含非AppIcon资源的情况。`)
	resignApp.Flags().StringVar(&EntitlementsPath, "entitlements", "", `【可选】自定义 entitlements.plist 文件路径，默认会解析IPA包，自动处理entitlements`)
	resignApp.Flags().BoolVar(&KeepTempDir, "keep-temp-dir", false, `【可选】保留临时文件目录，用于调试，默认为false（处理完成后删除临时文件）`)
	resignApp.Flags().StringVar(&AdditionalPlistPath, "additional-plist", "", `【可选】附加的 plist 文件路径，用于添加、替换复杂结构的Key，如CFBundleURLTypes，将与当前IPA中Info.plist顶层key进行合并或替换处理`)
	resignApp.Flags().StringVar(&RemovePlistKeys, "remove-plist-keys", "", `【可选】要删除的 Info.plist 中顶层 key，多个key用逗号分隔`)
	resignApp.Flags().StringVarP(&AddFilesPath, "add-files-path", "a", "", `【可选】要添加到.app/Contents目录的文件夹路径，该文件夹内的文件结构对应.app/Contents目录的根目录结构。可用于替换Launcher图、配置文件等 `)
	resignApp.Flags().StringVar(&DeleteFilePaths, "delete-files", "", `【可选】要从.app目录中删除的文件或文件夹路径，多个路径用逗号分隔，路径以.app/Contents根目录为基准`)
	resignApp.Flags().StringVar(&AppDevConfig, "app-dev-config", "", `【可选】请求dev后台App对应的配置文件所需参数，将请求到的配置文件替换到重签后的ipa中。格式：appid=1000000,env=0，可选参数host=xxx`)
	resignApp.Flags().StringVar(&LogOutputMode, "log-output", "console", `【可选】日志输出模式，可选值：console（控制台输出）、file（输出到文件），默认console`)

	// // Add flags to rpath command
	rpathAppCmd.Flags().StringVarP(&AppPath, "path", "p", "", `【必传】ipa文件路径`)

	// Add flags to build command
	buildAppCmd.Flags().StringVarP(&ProjectPath, "path", "p", "", `【必传】Xcode项目工程根路`)
	buildAppCmd.Flags().StringVar(&Workspace, "workspace", "", `【可选】workspace文件名（如MyApp.xcworkspace）`)
	buildAppCmd.Flags().StringVar(&Project, "project", "", `【可选】project文件名（如MyApp.xcodeproj）`)
	buildAppCmd.Flags().StringVar(&Scheme, "scheme", "", `【可选】构建scheme，如果不指定则自动获取`)
	buildAppCmd.Flags().StringVarP(&OutputPath, "output-dir", "o", "", `【可选】输出Build目录路径`)
	buildAppCmd.Flags().StringVar(&BundleID, "bundle-id", "", `【可选】Bundle ID，没传时默认读取工程设置的`)
	buildAppCmd.Flags().StringVar(&ProvisioningProfile, "provision", "", `【可选】ProvisioningProfile文件名或mobileprovision文件路径，未设置时根据bundleId和cert-type自动搜索mobileprovision`)
	buildAppCmd.Flags().StringVar(&CertificateType, "cert-type", "", `【可选】证书类型，可选值：dev(开发)、dis(提交到AppStore)、did(侧载发布) 。当未指定具体provision时，将根据此类型自动选择合适的provision和证书`)
	buildAppCmd.Flags().StringVar(&Configuration, "configuration", "Release", `【可选】构建配置，默认为Release`)
	buildAppCmd.Flags().StringVar(&XcodeBuildArgs, "xcodebuild-args", "", `【可选】追加到xcodebuild archive命令的额外参数，如：VALID_ARCHS=x86_64 -UseModernBuildSystem=YES`)
	buildAppCmd.Flags().StringVarP(&AddFilesPath, "add-files-path", "a", "", `【可选】要添加到.app/Contents目录的文件夹路径，该文件夹内的文件结构对应.app/Contents目录的根目录结构。可用于替换Launcher图、配置文件等 `)
	buildAppCmd.Flags().StringVar(&AppDevConfig, "app-dev-config", "", `【可选】请求dev后台App对应的配置文件所需参数，将请求到的配置文件替换到重签后的ipa中。格式：appid=1000000,env=0，可选参数host=xxx`)
	buildAppCmd.Flags().StringVar(&LogOutputMode, "log-output", "console", `【可选】日志输出模式，可选值：console（控制台输出）、file（输出到文件），默认console`)

	// Add flags to main ipa command
	appCmd.Flags().BoolVar(&ShowProfiles, "profiles", false, `【可选】列出macOS配置文件信息`)
	appCmd.Flags().BoolVar(&ProfilesOutputJSON, "json", false, `【可选】以JSON格式输出配置文件信息（需要与--profiles一起使用）`)
}

// 重新签名.app文件
func resignAppExecute(cmd *cobra.Command, args []string) {
	if AppPath == "" || (!strings.HasSuffix(AppPath, ".zip") && !strings.HasSuffix(AppPath, ".pkg")) {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:    -1,
				Message: fmt.Sprintf("【%s】路径不存在，请确认app.zip路径是否正确，只接受.zip和.pkg文件", AppPath),
				LogPath: "",
			}
			fmt.Print(result.ToJSON())
		} else {
			fmt.Printf("错误：【%s】路径不存在，请确认app.zip路径是否正确，只接受.zip和.pkg文件\n", AppPath)
		}
		return
	}

	// 验证证书类型参数
	if CertificateType != "" {
		certType := strings.ToLower(CertificateType)
		if certType != "dev" && certType != "dis" && certType != "did" {
			if LogOutputMode == "file" {
				result := &ipa.JsonModelResult{
					Code:    -1,
					Message: fmt.Sprintf("错误：不支持的证书类型 '%s'，支持的类型：dev(开发)、dis(提交到AppStore)、did(侧载发布)", CertificateType),
					LogPath: "",
				}
				fmt.Print(result.ToJSON())
			} else {
				fmt.Printf("错误：不支持的证书类型 '%s'，支持的类型：dev(开发)、dis(提交到AppStore)、did(侧载发布)\n", CertificateType)
			}
			return
		}
	}

	// 确保IPAPath是绝对路径
	absAppPath, _ := utility.EnsureAbsolutePath(AppPath)
	IPAPath = absAppPath

	// 初始化日志管理器
	var logFilePath string
	if LogOutputMode == "file" {
		// 生成默认日志文件路径
		ipaDir := filepath.Dir(IPAPath)
		fileBaseName := filepath.Base(IPAPath)
		appName := strings.TrimSuffix(fileBaseName, filepath.Ext(fileBaseName))
		timestamp := time.Now().Format("20060102_150405.000")
		logFilePath = filepath.Join(ipaDir, fmt.Sprintf("%s_resign_%s.txt", appName, timestamp))

		// 确保日志文件路径是绝对路径
		absLogFilePath, _ := utility.EnsureAbsolutePath(logFilePath)
		logFilePath = absLogFilePath

		if err := utility.InitLogger(utility.LoggerModeFile, logFilePath); err != nil {
			fmt.Printf("初始化日志文件失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	} else {
		if err := utility.InitLogger(utility.LoggerModeConsole, ""); err != nil {
			fmt.Printf("初始化控制台日志失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	}

	// 检查IPA文件是否存在
	if !utility.IsExist(IPAPath) {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:    -1,
				Message: fmt.Sprintf("【%s】路径不存在，请确认App路径是否正确", IPAPath),
				LogPath: logFilePath,
			}
			fmt.Print(result.ToJSON())
		} else {
			fmt.Printf("错误：【%s】路径不存在，请确认App路径是否正确\n", IPAPath)
		}
		return
	}

	// 确保其他路径参数是绝对路径
	if OutputPath != "" {
		absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
		OutputPath = utility.GetDir(absOutputPath)
	}

	if ProvisioningProfile != "" && strings.HasSuffix(ProvisioningProfile, ".provisionprofile") {
		absProvisioningProfile, _ := utility.EnsureAbsolutePath(ProvisioningProfile)
		ProvisioningProfile = absProvisioningProfile
	}

	if AppIconPath != "" {
		absAppIconPath, _ := utility.EnsureAbsolutePath(AppIconPath)
		AppIconPath = absAppIconPath
	}

	if EntitlementsPath != "" {
		absEntitlementsPath, _ := utility.EnsureAbsolutePath(EntitlementsPath)
		EntitlementsPath = absEntitlementsPath
	}

	// 确保附加的plist文件路径是绝对路径
	if AdditionalPlistPath != "" {
		absAdditionalPlistPath, _ := utility.EnsureAbsolutePath(AdditionalPlistPath)
		AdditionalPlistPath = absAdditionalPlistPath
	}

	if AddFilesPath != "" {
		absAddFilesPath, _ := utility.EnsureAbsolutePath(AddFilesPath)
		AddFilesPath = absAddFilesPath
	}

	// 创建重签名配置
	config := &ipa.ResignConfig{
		IPAPath:             IPAPath,
		OutputPath:          OutputPath,
		BundleID:            BundleID,
		ProvisioningProfile: ProvisioningProfile,
		Certificate:         Certificate,
		CertificateType:     CertificateType,
		DisplayName:         DisplayName,
		AppVersion:          AppVersion,
		BuildVersion:        BuildVersion,
		AppIconPath:         AppIconPath,
		LaunchScreen:        "", //macOS没有 LaunchScreen（启动画面）的机制
		ForceSwiftUpdate:    ForceSwiftUpdate,
		EntitlementsPath:    EntitlementsPath,
		KeepTempDir:         KeepTempDir,
		AdditionalPlistPath: AdditionalPlistPath,
		RemovePlistKeys:     RemovePlistKeys,
		DeleteFilePaths:     DeleteFilePaths,
		AddFilesPath:        AddFilesPath,
		AppDevConfig:        AppDevConfig,
		Platform:            utility.PlatformOS_OSX,
	}

	// 执行重签名
	outputPath, err := ipa.ResignIPAExe(config)

	// 如果是文件输出模式，输出JSON结果
	if LogOutputMode == "file" {
		if err != nil {
			retuslt := ipa.JsonModelResult{
				Code:    -1,
				Message: err.Error(),
				LogPath: logFilePath,
			}
			fmt.Print(retuslt.ToJSON())
		} else {
			retuslt := ipa.JsonModelResult{
				Code:       0,
				Message:    "success",
				LogPath:    logFilePath,
				OutputPath: outputPath,
			}
			fmt.Print(retuslt.ToJSON())
		}

	} else {
		// 控制台模式，输出传统格式
		if err != nil {
			fmt.Printf("重签名失败: %s\n", err.Error())
			os.Exit(1)
		}
	}
}

// 验证和修复rpath配置
func rpathAppExecute(cmd *cobra.Command, args []string) {
	if AppPath == "" {
		fmt.Println("错误：必须指定ipa文件路径")
		fmt.Println("使用方法：onetools app rpath -p /path/to/myDemo.app")
		return
	}

	if !strings.HasSuffix(AppPath, ".app") && !strings.HasSuffix(AppPath, ".zip") {
		fmt.Println("错误：只接受.app或.zip文件")
		fmt.Println("使用方法：onetools app rpath -p /path/to/myDemo.app")
		return
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(AppPath)
	AppPath = absIPAPath

	if !utility.IsExist(AppPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认App路径是否正确", AppPath))
	}

	// // 执行rpath验证和修复
	err := ipa.VerifyAndFixRpathExe(AppPath, utility.PlatformOS_OSX)
	if err != nil {
		fmt.Printf("rpath验证失败: %v\n", err)
		os.Exit(1)
	}
}

// 构建macOS项目
func buildAppExecute(cmd *cobra.Command, args []string) {
	if ProjectPath == "" {
		fmt.Println("错误：必须指定项目路径")
		fmt.Println("使用方法：onetools app build --project-path /path/to/project")
		return
	}

	// 确保ProjectPath是绝对路径
	absProjectPath, _ := utility.EnsureAbsolutePath(ProjectPath)
	ProjectPath = absProjectPath

	// 初始化日志管理器
	var logFilePath string
	if LogOutputMode == "file" {
		// 生成默认日志文件路径
		timestamp := time.Now().Format("20060102_150405.000")
		logFilePath = filepath.Join(ProjectPath, fmt.Sprintf("build_%s.txt", timestamp))

		// 确保日志文件路径是绝对路径
		absLogFilePath, _ := utility.EnsureAbsolutePath(logFilePath)
		logFilePath = absLogFilePath

		if err := utility.InitLogger(utility.LoggerModeFile, logFilePath); err != nil {
			fmt.Printf("初始化日志文件失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	} else {
		if err := utility.InitLogger(utility.LoggerModeConsole, ""); err != nil {
			fmt.Printf("初始化控制台日志失败: %v\n", err)
			return
		}
		defer utility.CloseLogger()
	}

	// 验证证书类型参数
	if CertificateType != "" {
		certType := strings.ToLower(CertificateType)
		if certType != "dev" && certType != "dis" && certType != "did" {
			if LogOutputMode == "file" {
				result := &ipa.JsonModelResult{
					Code:    -1,
					Message: fmt.Sprintf("错误：不支持的证书类型 '%s'，支持的类型：dev(开发)、dis(提交到AppStore)、did(侧载发布)", CertificateType),
					LogPath: "",
				}
				fmt.Print(result.ToJSON())
			} else {
				fmt.Printf("错误：不支持的证书类型 '%s'，支持的类型：dev(开发)、dis(提交到AppStore)、did(侧载发布)\n", CertificateType)
			}
			return
		}
	}

	// 创建构建配置
	buildConfig := &ipa.BuildConfig{
		ProjectPath:         ProjectPath,
		Workspace:           Workspace,
		Project:             Project,
		Scheme:              Scheme,
		Configuration:       Configuration,
		OutputPath:          OutputPath,
		BundleID:            BundleID,
		ProvisioningProfile: ProvisioningProfile,
		CertificateType:     CertificateType,
		Platform:            utility.PlatformOS_OSX,
		XcodeBuildArgs:      XcodeBuildArgs,
		AppDevConfig:        AppDevConfig,
		AddFilesPath:        AddFilesPath,
	}

	// 执行构建
	output, err := ipa.BuildIPAExe(buildConfig)
	if err != nil {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:       -1,
				Message:    err.Error(),
				LogPath:    logFilePath,
				OutputPath: output,
			}
			fmt.Print(result.ToJSON())
		} else {
			utility.LogPrintError(fmt.Sprintf("构建失败: %v", err))
		}
		os.Exit(1)
	} else {
		if LogOutputMode == "file" {
			result := &ipa.JsonModelResult{
				Code:       0,
				Message:    "构建完成",
				LogPath:    logFilePath,
				OutputPath: output,
			}
			fmt.Print(result.ToJSON())
		} else {
			utility.LogPrintSuccess("构建完成")
		}
	}
}

// app 主命令执行函数
func appExecute(cmd *cobra.Command, args []string) {
	if ShowProfiles {
		// 执行配置文件列表功能
		err := ipa.ListProvisioningProfiles(ProfilesOutputJSON, utility.PlatformOS_OSX)
		if err != nil {
			fmt.Printf("获取配置文件信息失败: %v\n", err)
			os.Exit(1)
		}
		return
	}

	// 如果没有使用 --profiles，显示帮助信息
	cmd.Help()
}
