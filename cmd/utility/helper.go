package utility

import (
	"bufio"
	"crypto/tls"
	"fmt"
	"io"
	"io/fs"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// 日志输出颜色常量
const (
	ColorRed    = "\033[0;31m"
	ColorGreen  = "\033[0;32m"
	ColorYellow = "\033[1;33m"
	ColorNC     = "\033[0m"
)

type PlatformOS int32

const (
	PlatformOS_IOS     PlatformOS = 0
	PlatformOS_OSX     PlatformOS = 1
	PlatformOS_PS4     PlatformOS = 2 //目前PS没区分4和5，备用
	PlatformOS_PS5     PlatformOS = 3 //目前PS没区分4和5，备用
	PlatformOS_PS      PlatformOS = 4 //目前PS没区分4和5，统一使用ps
	PlatformOS_Android PlatformOS = 5
	PlatformOS_Windows PlatformOS = 6
)

var projectPath string
var platformOS PlatformOS

func SaveProjectPath(projPath string) {
	projectPath = projPath
}

func GetProjectPath() string {
	return projectPath
}

func SetPlatformOS(platform PlatformOS) {
	platformOS = platform
}

func GetPlatformOS() PlatformOS {
	return platformOS
}

func PathSplitString() string {
	return "/"
}

func PathRefactorSplit(path string) string {
	if !(strings.HasSuffix(path, PathSplitString())) { // 如果未以分隔符结尾，则主动添加文件分隔符
		path = path + PathSplitString()
	}
	return path
}

func GetCommanPath(str1 string, str2 string) string {
	// write code here
	l1 := len(str1) //行
	l2 := len(str2) // 列
	if l1 == 0 || l2 == 0 {
		return ""
	}

	// 第一行 第一列都为空
	//   主要是为了好处理 dp[0][0]
	dp := make([][]int, l1+1)
	for i := 0; i < l1+1; i++ {
		dp[i] = make([]int, l2+1)
	}

	max := 0
	end := 0

	//  注意 l1+1   l2+1
	//  二维数组dp[i][j]表示第一个字符串前i个字符和第二个字符串前j个字符组成的最长公共字符串的长度
	//  字符相等 dp[i][j] = dp[i-1][j-1] + 1  否则 dp[i][j] = 0
	for i := 1; i < l1+1; i++ {
		for j := 1; j < l2+1; j++ {
			if str1[i-1] == str2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = 0
			}

			if dp[i][j] > max {
				max = dp[i][j]
				end = i // 注意
			}
		}
	}

	if max == 0 {
		return ""
	}

	return str1[end-max : end]
}

// 获取文件
func GetFilesFromDir(dirPath string, complete func(filePath string)) {
	err := filepath.Walk(dirPath, func(path string, info fs.FileInfo, err error) error {

		if info == nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		_, fileName := filepath.Split(path)

		if strings.HasPrefix(fileName, ".") {
			return nil
		}

		if complete != nil {
			complete(path)
		}

		return nil
	})

	if err != nil {
		panic(err.Error())
	}
}

// 非递归获取目录下子目录
func GetSubDirsNotRecursive(dirPath string) []string {
	if !IsExist(dirPath) {
		return []string{}
	}

	var dirs []string
	files, _ := os.ReadDir(dirPath)
	for _, file := range files {
		if file.IsDir() {
			dirs = append(dirs, file.Name())
		}
	}
	return dirs
}

// 递归获取子目录
func GetSubContentFromDir(dirPath string, complete func(path string, info fs.FileInfo)) {
	err := filepath.Walk(dirPath, func(path string, info fs.FileInfo, err error) error {

		if info == nil {
			return err
		}

		_, name := filepath.Split(path)

		if strings.HasPrefix(name, ".") {
			return nil
		}

		if complete != nil {
			complete(path, info)
		}

		return nil
	})

	if err != nil {
		panic(err.Error())
	}
}

func AllFilesAndDirectoriesFromPath(dir string, callback func(dirPath string, d fs.FileInfo)) {

	filePathList, err := filepath.Glob(filepath.Join(dir, "*"))
	if err != nil {
		log.Printf("读取当前目录【%s】下的信息失败：%s\n", dir, err.Error())

	}

	for _, filePath := range filePathList {
		_, fileName := filepath.Split(filePath)
		info, err := os.Stat(filePath)
		if err != nil {
			log.Println(err)
		}

		if strings.HasPrefix(fileName, ".") || strings.HasPrefix(fileName, "__MACOSX") {
			continue
		}

		if info.IsDir() {
			if callback != nil {
				callback(filePath, info)
			}
			AllFilesAndDirectoriesFromPath(filePath, callback)
		} else {
			if callback != nil {
				callback(filePath, info)
			}
		}
	}
}

// 获取文件内容
func GetFileContent(filePath string) string {
	_, err := os.Stat(filePath)
	if err != nil {
		return ""
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		panic(err.Error())
	}

	return string(content)
}

func WriteContentToFile(filePath string, content string) {

	if content == "" {
		return
	}
	contentByte := []byte(content)
	err := os.WriteFile(filePath, contentByte, 0777)

	if err != nil {
		panic(err.Error())
	}
}

func GetPluginPath(projectPath string, pluginName string, sdkDirPath string) string {
	pluginSuperPath := ""

	if filepath.IsAbs(pluginName) {
		return pluginName
	}

	if IsUEProject(projectPath) {
		if len(sdkDirPath) > 0 {
			pluginSuperPath = sdkDirPath
		} else {
			pluginSuperPath = filepath.Join(projectPath, "Plugins")
		}

	} else if IsUnityProject(projectPath) {
		if GetPlatformOS() == PlatformOS_OSX {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "Mac")
		} else if GetPlatformOS() == PlatformOS_IOS {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "iOS")
		} else if GetPlatformOS() == PlatformOS_PS4 {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "PS4")
		} else if GetPlatformOS() == PlatformOS_PS5 {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "PS5")
		} else if GetPlatformOS() == PlatformOS_PS {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "PS")
		} else if GetPlatformOS() == PlatformOS_Android {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "Android")
		} else if GetPlatformOS() == PlatformOS_Windows {
			pluginSuperPath = filepath.Join(projectPath, "Assets", "Plugins", "Windows")
		}
	} else {
		pluginSuperPath = projectPath
	}

	return filepath.Join(pluginSuperPath, pluginName)
}

func GetDownloadNativeSDKPath(projectPath string) string {
	downloadPath := ""
	if IsUEProject(projectPath) {
		downloadPath = filepath.Join(projectPath, "Plugins")
	} else if IsUnityProject(projectPath) {
		if GetPlatformOS() == PlatformOS_OSX {
			downloadPath = filepath.Join(projectPath, "WPSDKConfig", "Mac")
		} else if GetPlatformOS() == PlatformOS_IOS {
			downloadPath = filepath.Join(projectPath, "WPSDKConfig", "iOS")
		} else if GetPlatformOS() == PlatformOS_PS4 {
			downloadPath = filepath.Join(projectPath, "WPSDKConfig", "PS4")
		} else if GetPlatformOS() == PlatformOS_PS5 {
			downloadPath = filepath.Join(projectPath, "WPSDKConfig", "PS5")
		} else if GetPlatformOS() == PlatformOS_PS {
			downloadPath = filepath.Join(projectPath, "WPSDKConfig", "PS")
		}
	} else {
		downloadPath = projectPath
	}
	return downloadPath
}

// 字符串数组中是否有相等的字符串
func IsContain(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func ContainsKeywordInFile(filename, keyword string) (bool, error) {
	file, err := os.Open(filename)
	if err != nil {
		return false, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := scanner.Text()
		if strings.Contains(line, keyword) {
			return true, nil
		}
	}

	return false, nil
}

// DownloadFile 下载文件到指定目录
func DownloadFile(url string, destDir string) (string, error) {
	// 获取文件名
	filename := filepath.Base(url)
	return DownloadFileWithName(url, destDir, filename)
}

func DownloadFileWithName(url string, destDir string, downloadFileName string) (string, error) {
	// 获取文件名
	filename := downloadFileName

	// 构造文件的本地路径
	localPath := filepath.Join(destDir, filename)

	// 创建目标目录（如果不存在）
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return "", err
	}

	// 创建自定义的 http.Client，忽略 SSL 证书验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	// 发送 HTTP 请求
	resp, err := client.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("request Error,http status code: %d", resp.StatusCode)
	}

	// 创建文件
	out, err := os.Create(localPath)
	if err != nil {
		return "", err
	}
	defer out.Close()

	// 将文件内容从 HTTP 响应写入到本地文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return "", err
	}

	// 返回文件路径
	return localPath, nil
}

func GetNativeSDKName() string {
	if GetPlatformOS() == PlatformOS_OSX {
		return "MacNativeSDK"
	} else if GetPlatformOS() == PlatformOS_PS4 {
		return "PS4NativeSDK"
	} else if GetPlatformOS() == PlatformOS_PS5 {
		return "PS5NativeSDK"
	} else if GetPlatformOS() == PlatformOS_PS {
		return "PSNativeSDK"
	} else if GetPlatformOS() == PlatformOS_Android {
		return "AndroidNativeSDK"
	} else if GetPlatformOS() == PlatformOS_Windows {
		return "WindowsNativeSDK"
	} else {
		return "iOSNativeSDK"
	}
}

func GetTimeStr() string {
	timeZone := time.FixedZone("CST", 8*3600)
	return time.Now().In(timeZone).Format("2006-01-02 15:04:05")
}

func GetSubModules(projectPath string) string {
	gradleDirPath := GetPluginPath(projectPath, "Gradles", "")
	directories := GetSubDirsNotRecursive(gradleDirPath)

	subModules := []string{}
	for _, directory := range directories {
		subModules = append(subModules, filepath.Join("Gradles", directory))
	}

	return strings.Join(subModules, ",")
}

func GetDir(path string) string {
	ext := filepath.Ext(path)
	if ext == "" {
		// 没有扩展名，直接认为是文件夹路径，返回自己
		return path
	}
	// 有扩展名，当作文件，返回上层目录
	return filepath.Dir(path)
}
