package utility

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"
)

// LoggerMode 日志输出模式
type LoggerMode int

const (
	LoggerModeConsole LoggerMode = iota
	LoggerModeFile
)

// Logger 日志管理器
type Logger struct {
	mode     LoggerMode
	file     *os.File
	writer   io.Writer
	mutex    sync.Mutex
	filePath string
}

var globalLogger *Logger
var loggerMutex sync.Mutex

// InitLogger 初始化日志管理器
func InitLogger(mode LoggerMode, filePath string) error {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	// 如果已经有logger，先关闭
	if globalLogger != nil {
		globalLogger.Close()
	}

	logger := &Logger{
		mode: mode,
	}

	switch mode {
	case LoggerModeConsole:
		logger.writer = os.Stdout
	case LoggerModeFile:
		if filePath == "" {
			return fmt.Errorf("文件模式下必须指定日志文件路径")
		}

		// 创建目录
		dir := filepath.Dir(filePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %w", err)
		}

		file, err := os.Create(filePath)
		if err != nil {
			return fmt.Errorf("创建日志文件失败: %w", err)
		}
		logger.file = file
		logger.writer = file
		logger.filePath = filePath
	}

	globalLogger = logger
	return nil
}

// GetLogger 获取全局日志管理器
func GetLogger() *Logger {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()
	return globalLogger
}

// Close 关闭日志管理器
func (l *Logger) Close() {
	if l.file != nil {
		l.file.Close()
		l.file = nil
	}
}

// GetLogFilePath 获取日志文件路径
func (l *Logger) GetLogFilePath() string {
	return l.filePath
}

// writeLog 写入日志
func (l *Logger) writeLog(level, msg string) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	if l.mode == LoggerModeConsole {
		// 控制台输出保持原有的颜色格式
		switch level {
		case "ERROR":
			if runtime.GOOS == "windows" {
				fmt.Fprintf(l.writer, "[ERROR] %s\n", msg)
			} else {
				fmt.Fprintf(l.writer, "%s[ERROR]%s %s\n", ColorRed, ColorNC, msg)
			}
		case "SUCCESS":
			if runtime.GOOS == "windows" {
				fmt.Fprintf(l.writer, "[SUCCESS] %s\n", msg)
			} else {
				fmt.Fprintf(l.writer, "%s[SUCCESS]%s %s\n", ColorGreen, ColorNC, msg)
			}
		case "WARNING":
			if runtime.GOOS == "windows" {
				fmt.Fprintf(l.writer, "[WARNING] %s\n", msg)
			} else {
				fmt.Fprintf(l.writer, "%s[WARNING]%s %s\n", ColorYellow, ColorNC, msg)
			}
		case "INFO":
			fmt.Fprintf(l.writer, "[INFO] %s\n", msg)
		}
	} else {
		// 文件输出使用统一格式，包含时间戳
		fmt.Fprintf(l.writer, "%s [%s] %s\n", timestamp, level, msg)
	}
}

// LogError 输出错误日志
func (l *Logger) LogError(msg string) {
	l.writeLog("ERROR", msg)
}

// LogSuccess 输出成功日志
func (l *Logger) LogSuccess(msg string) {
	l.writeLog("SUCCESS", msg)
}

// LogWarning 输出警告日志
func (l *Logger) LogWarning(msg string) {
	l.writeLog("WARNING", msg)
}

// LogInfo 输出信息日志
func (l *Logger) LogInfo(msg string) {
	l.writeLog("INFO", msg)
}

// 统一的日志输出方法，直接控制台打印
func PrintError(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[ERROR] %s\n", msg)
	} else {
		fmt.Printf("%s[ERROR]%s %s\n", ColorRed, ColorNC, msg)
	}
}

func PrintSuccess(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[SUCCESS] %s\n", msg)
	} else {
		fmt.Printf("%s[SUCCESS]%s %s\n", ColorGreen, ColorNC, msg)
	}
}

func PrintWarning(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[WARNING] %s\n", msg)
	} else {
		fmt.Printf("%s[WARNING]%s %s\n", ColorYellow, ColorNC, msg)
	}
}

func PrintInfo(msg string) {
	if runtime.GOOS == "windows" {
		fmt.Printf("[INFO] %s\n", msg)
	} else {
		fmt.Printf("[INFO] %s\n", msg)
	}
}

// 全局日志函数，兼容原有代码
func LogPrintError(msg string) {
	if logger := GetLogger(); logger != nil {
		logger.LogError(msg)
	} else {
		PrintError(msg)
	}
}

func LogPrintSuccess(msg string) {
	if logger := GetLogger(); logger != nil {
		logger.LogSuccess(msg)
	} else {
		PrintSuccess(msg)
	}
}

func LogPrintWarning(msg string) {
	if logger := GetLogger(); logger != nil {
		logger.LogWarning(msg)
	} else {
		PrintWarning(msg)
	}
}

func LogPrintInfo(msg string) {
	if logger := GetLogger(); logger != nil {
		logger.LogInfo(msg)
	} else {
		PrintInfo(msg)
	}
}

// CloseLogger 关闭全局日志管理器
func CloseLogger() {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
		globalLogger = nil
	}
}
