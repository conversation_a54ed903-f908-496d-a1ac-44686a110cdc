package ipa

import (
	"fmt"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// UnzipPkgExe 解压macOS pkg包到指定目录
// pkgPath: pkg包的路径
// destPath: 解压目标目录路径
func UnzipPkgExe(pkgPath, destPath string) error {
	utility.LogPrintInfo(fmt.Sprintf("开始解压pkg包: %s", pkgPath))

	// 检查pkg文件是否存在
	if !utility.IsExist(pkgPath) {
		return fmt.Errorf("pkg文件不存在: %s", pkgPath)
	}

	// 检查是否为pkg文件
	if !strings.HasSuffix(strings.ToLower(pkgPath), ".pkg") {
		return fmt.Errorf("文件不是pkg格式: %s", pkgPath)
	}

	// 创建目标目录
	if err := os.MkdirAll(destPath, 0755); err != nil {
		return fmt.Errorf("无法创建目标目录: %w", err)
	}

	// 创建临时目录用于解压
	tempDir := filepath.Join(destPath, "temp_pkg_extract")
	defer os.RemoveAll(tempDir) // 清理临时目录

	utility.LogPrintInfo("使用pkgutil命令解压pkg包...")

	// 使用pkgutil命令解压pkg包
	// pkgutil --expand 命令可以将pkg包解压为目录结构
	cmd := exec.Command("pkgutil", "--expand", pkgPath, tempDir)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("pkgutil解压失败: %s, %w", string(output), err)
	}

	utility.LogPrintInfo("pkg包解压完成，开始提取Payload...")

	// 查找并解压Payload文件
	err = extractPayloadFromPkg(tempDir, destPath)
	if err != nil {
		return fmt.Errorf("提取Payload失败: %w", err)
	}

	utility.LogPrintSuccess(fmt.Sprintf("pkg包解压完成: %s", destPath))
	return nil
}

// extractPayloadFromPkg 从解压的pkg目录中提取Payload
func extractPayloadFromPkg(pkgDir, destPath string) error {
	var payloadPath string

	// 遍历pkg目录查找Payload文件
	err := filepath.Walk(pkgDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 查找Payload文件（通常在某个.pkg子目录中）
		if !info.IsDir() && strings.Contains(strings.ToLower(info.Name()), "payload") {
			payloadPath = path
			utility.LogPrintInfo(fmt.Sprintf("找到Payload文件: %s", path))
			return filepath.SkipDir // 找到后停止搜索
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("搜索Payload文件失败: %w", err)
	}

	if payloadPath == "" {
		return fmt.Errorf("未找到Payload文件")
	}

	// 使用cpio命令解压Payload
	utility.LogPrintInfo("使用cpio命令解压Payload...")

	// 创建解压命令: cat payload | gunzip -dc | cpio -i
	// 这是解压macOS pkg中Payload的标准方法
	cmd := exec.Command("sh", "-c", fmt.Sprintf("cd '%s' && cat '%s' | gunzip -dc | cpio -i", destPath, payloadPath))
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("cpio解压Payload失败: %s, %w", string(output), err)
	}

	utility.LogPrintInfo("Payload解压完成")

	// 检查是否成功解压出.app文件
	appFound := false
	entries, err := os.ReadDir(destPath)
	if err != nil {
		return fmt.Errorf("读取解压目录失败: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			appFound = true
			utility.LogPrintInfo(fmt.Sprintf("成功解压出.app文件: %s", entry.Name()))
			break
		}
	}

	if !appFound {
		utility.LogPrintWarning("未在根目录找到.app文件，可能在子目录中")
		// 递归查找.app文件
		err = filepath.Walk(destPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if info.IsDir() && strings.HasSuffix(info.Name(), ".app") {
				utility.LogPrintInfo(fmt.Sprintf("在子目录中找到.app文件: %s", path))
				appFound = true
				return filepath.SkipDir
			}
			return nil
		})

		if err != nil {
			return fmt.Errorf("搜索.app文件失败: %w", err)
		}
	}

	if !appFound {
		return fmt.Errorf("解压后未找到.app文件，可能不是有效的应用程序pkg包")
	}

	return nil
}
