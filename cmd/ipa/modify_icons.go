package ipa

import (
	"fmt"
	"image"
	_ "image/png"
	"onetools/cmd/utility"
	"os"
	"path/filepath"
	"strings"

	"howett.net/plist"
)

// isAssetsXcassetsPath 检查路径是否为Assets.xcassets格式
func isAssetsXcassetsPath(path string) bool {
	// 检查路径是否存在且为目录
	if info, err := os.Stat(path); err == nil && info.IsDir() {
		// 检查是否以.xcassets结尾
		if strings.HasSuffix(path, ".xcassets") {
			return true
		}
	}
	return false
}

// validatePngIcon 验证PNG图标文件
func validatePngIcon(path string) error {
	// 验证图标格式和尺寸
	file, err := os.Open(path)
	if err != nil {
		return fmt.Errorf("无法打开图标文件: %s, 错误: %v", path, err)
	}
	defer file.Close()

	// 检查是否为PNG格式
	img, format, err := image.DecodeConfig(file)
	if err != nil {
		return fmt.Errorf("无法解析图标文件: %s, 错误: %v", path, err)
	}

	if format != "png" {
		return fmt.Errorf("图标必须是PNG格式，当前格式: %s, 文件: %s", format, path)
	}

	// 检查尺寸是否为1024x1024
	if img.Width != 1024 || img.Height != 1024 {
		return fmt.Errorf("图标尺寸必须是1024x1024像素，当前尺寸: %dx%d, 文件: %s", img.Width, img.Height, path)
	}

	// 重置文件指针
	file.Seek(0, 0)

	// 检查是否包含alpha通道（这需要完整解码图像）
	fullImg, _, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("无法完全解码图标: %s, 错误: %v", path, err)
	}

	// 检查是否有alpha通道
	if _, hasAlpha := fullImg.(interface{ Opaque() bool }); hasAlpha {
		// 如果图像实现了Opaque()方法，检查它是否完全不透明
		opaqueImg := fullImg.(interface{ Opaque() bool })
		if !opaqueImg.Opaque() {
			return fmt.Errorf("图标不能包含alpha通道（透明度）: %s", path)
		}
	}

	return nil
}

// 替换应用图标
func replaceAppIcon(appPath string, iconPath string, tempDir string) error {
	if iconPath == "" {
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("替换应用图标: %s", iconPath))

	// 将iconPath按逗号分隔为数组
	iconPathsArray := strings.Split(iconPath, ",")
	var validIconPaths []string
	var assetsXcassetsPath string
	var hasAssetsXcassets bool

	// 遍历每个图标路径，分类处理Assets.xcassets和PNG文件
	for _, path := range iconPathsArray {
		if path == "" {
			continue
		}

		// 1. 检查iconPath是否存在
		if _, err := os.Stat(path); os.IsNotExist(err) {
			return fmt.Errorf("图标文件不存在: %s", path)
		}

		// 2. 检查是否为Assets.xcassets格式
		if isAssetsXcassetsPath(path) {
			if hasAssetsXcassets {
				return fmt.Errorf("只能指定一个Assets.xcassets路径")
			}
			hasAssetsXcassets = true
			assetsXcassetsPath = path
			utility.LogPrintInfo(fmt.Sprintf("检测到Assets.xcassets格式: %s", path))
		} else {
			// 3. 验证PNG图标文件
			if err := validatePngIcon(path); err != nil {
				return err
			}
			// 通过所有验证，添加到有效图标路径列表
			validIconPaths = append(validIconPaths, path)
			utility.LogPrintSuccess(fmt.Sprintf("PNG图标验证通过: %s", path))
		}
	}

	// 4. 优先处理Assets.xcassets格式
	var finalIconPaths []string
	if hasAssetsXcassets {
		utility.LogPrintInfo(fmt.Sprintf("优先使用Assets.xcassets格式: %s", assetsXcassetsPath))
		finalIconPaths = []string{assetsXcassetsPath}
		if len(validIconPaths) > 0 {
			utility.LogPrintInfo("忽略PNG文件，因为已指定Assets.xcassets格式")
		}
	} else {
		utility.LogPrintInfo(fmt.Sprintf("使用PNG文件，共有 %d 个有效图标文件", len(validIconPaths)))
		finalIconPaths = validIconPaths
	}

	if len(finalIconPaths) == 0 {
		return fmt.Errorf("没有找到有效的图标文件")
	}

	// 5. 调用alternateIcons命令生成图标
	utility.LogPrintInfo("生成应用图标资源...")
	// 创建输出目录在tempDir下
	outputDir := filepath.Join(tempDir, "newAppIcon")
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("创建图标输出目录失败: %v", err)
	}

	// 使用GenerateAlternateIconsExe函数直接生成图标
	err := GenerateAlternateIconsExe(finalIconPaths, "", outputDir)
	if err != nil {
		return fmt.Errorf("生成图标资源失败: %v", err)
	}

	// 6. 扫描并删除现有的AppIcon文件
	utility.LogPrintInfo("扫描并删除现有的AppIcon文件...")
	files, err := os.ReadDir(appPath)
	if err != nil {
		return fmt.Errorf("读取应用目录失败: %v", err)
	}

	// 删除以AppIcon开头，以.png结尾的文件
	var deletedCount int
	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "AppIcon") && strings.HasSuffix(file.Name(), ".png") {
			filePath := filepath.Join(appPath, file.Name())
			if err := os.Remove(filePath); err != nil {
				utility.LogPrintWarning(fmt.Sprintf("删除原有图标文件失败 %s: %v", file.Name(), err))
			} else {
				deletedCount++
				utility.LogPrintInfo(fmt.Sprintf("已删除原有图标文件: %s", file.Name()))
			}
		}
	}
	utility.LogPrintInfo(fmt.Sprintf("共删除 %d 个原有图标文件", deletedCount))

	// 7. 替换生成的图标文件到应用目录
	buildDir := filepath.Join(tempDir, "newAppIcon", "build")
	if _, err := os.Stat(buildDir); os.IsNotExist(err) {
		return fmt.Errorf("未找到生成的图标资源目录: %s", buildDir)
	}

	// 查找并替换.png文件
	pngFiles, err := filepath.Glob(filepath.Join(buildDir, "*.png"))
	if err != nil {
		return fmt.Errorf("查找生成的PNG文件失败: %v", err)
	}

	for _, pngFile := range pngFiles {
		fileName := filepath.Base(pngFile)
		targetPath := filepath.Join(appPath, fileName)

		// 强制替换，即使目标文件不存在
		if err := copyFile(pngFile, targetPath); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("替换图标文件失败 %s: %v", fileName, err))
		} else {
			utility.LogPrintSuccess(fmt.Sprintf("成功替换图标: %s", fileName))
		}
	}

	// 查找并替换Assets.car文件
	carFile := filepath.Join(buildDir, "Assets.car")
	if _, err := os.Stat(carFile); err == nil {
		targetCarPath := filepath.Join(appPath, "Assets.car")
		if err := copyFile(carFile, targetCarPath); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("替换Assets.car文件失败: %v", err))
		} else {
			utility.LogPrintSuccess("成功替换Assets.car文件")
		}
	}

	// 8. 合并partial.plist和目标.app下的Info.plist
	partialPlistPath := filepath.Join(buildDir, "partial.plist")
	if _, err := os.Stat(partialPlistPath); err == nil {
		utility.LogPrintInfo("检测到partial.plist文件，准备合并到Info.plist")

		targetInfoPlistPath := filepath.Join(appPath, "Info.plist")
		if _, err := os.Stat(targetInfoPlistPath); err == nil {
			// 调用MergePlistFiles方法合并plist文件
			mergedMap, err := utility.MergePlistFiles(targetInfoPlistPath, partialPlistPath)
			if err != nil {
				utility.LogPrintWarning(fmt.Sprintf("合并partial.plist到Info.plist失败: %v", err))
			} else {
				// 将合并后的内容写回Info.plist
				output, err := plist.MarshalIndent(mergedMap, plist.XMLFormat, "\t")
				if err != nil {
					utility.LogPrintWarning(fmt.Sprintf("序列化合并后的Info.plist失败: %v", err))
				} else {
					err = os.WriteFile(targetInfoPlistPath, output, 0644)
					if err != nil {
						utility.LogPrintWarning(fmt.Sprintf("写入合并后的Info.plist失败: %v", err))
					} else {
						utility.LogPrintSuccess("成功合并partial.plist到Info.plist")
					}
				}
			}
		} else {
			utility.LogPrintWarning(fmt.Sprintf("未找到目标Info.plist文件: %s", targetInfoPlistPath))
		}
	} else {
		utility.LogPrintInfo("未检测到partial.plist文件，跳过合并步骤")
	}

	utility.LogPrintSuccess("应用图标替换完成")
	return nil
}

// 替换OSX应用图标
func replaceOSXAppIcon(appPath string, iconPath string, tempDir string) error {
	if iconPath == "" {
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("替换应用图标: %s", iconPath))

	// 将iconPath按逗号分隔为数组
	iconPathsArray := strings.Split(iconPath, ",")
	var validIconPaths []string
	var assetsXcassetsPath string
	var hasAssetsXcassets bool

	// 遍历每个图标路径，分类处理Assets.xcassets和PNG文件
	if len(iconPathsArray) > 0 {
		iconResPath := iconPathsArray[0]
		// 1. 检查iconPath是否存在
		if _, err := os.Stat(iconResPath); os.IsNotExist(err) {
			return fmt.Errorf("图标文件不存在: %s", iconResPath)
		}

		// 2. 检查是否为Assets.xcassets格式
		if isAssetsXcassetsPath(iconResPath) {
			hasAssetsXcassets = true
			assetsXcassetsPath = iconResPath
			utility.LogPrintInfo(fmt.Sprintf("检测到Assets.xcassets格式: %s", iconResPath))
		} else {
			// 3. 验证PNG图标文件
			if err := validatePngIcon(iconResPath); err != nil {
				return err
			}
			// 通过所有验证，添加到有效图标路径列表
			validIconPaths = append(validIconPaths, iconResPath)
			utility.LogPrintSuccess(fmt.Sprintf("PNG图标验证通过: %s", iconResPath))
		}
	}

	// 4. 优先处理Assets.xcassets格式
	var finalIconPaths []string
	if hasAssetsXcassets {
		utility.LogPrintInfo(fmt.Sprintf("优先使用Assets.xcassets格式: %s", assetsXcassetsPath))
		finalIconPaths = []string{assetsXcassetsPath}
		if len(validIconPaths) > 0 {
			utility.LogPrintInfo("忽略PNG文件，因为已指定Assets.xcassets格式")
		}
	} else {
		utility.LogPrintInfo(fmt.Sprintf("使用PNG文件，共有 %d 个有效图标文件", len(validIconPaths)))
		finalIconPaths = validIconPaths
	}

	if len(finalIconPaths) == 0 {
		return fmt.Errorf("没有找到有效的图标文件")
	}

	// 5. 调用alternateIcons命令生成图标
	utility.LogPrintInfo("生成应用图标资源...")
	// 创建输出目录在tempDir下
	outputDir := filepath.Join(tempDir, "newAppIcon")
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("创建图标输出目录失败: %v", err)
	}

	// 使用GenerateAlternateIconsExe函数直接生成图标
	err := GenerateMacOSIconsExe(finalIconPaths, "", outputDir)
	if err != nil {
		return fmt.Errorf("生成图标资源失败: %v", err)
	}

	// 7. 替换生成的图标文件到应用目录
	buildDir := filepath.Join(tempDir, "newAppIcon", "build")
	if _, err := os.Stat(buildDir); os.IsNotExist(err) {
		return fmt.Errorf("未找到生成的图标资源目录: %s", buildDir)
	}

	appResourcesPath := filepath.Join(appPath, "Resources")
	// 替换AppIcon.icns文件
	appIconIcnsFile := filepath.Join(buildDir, "AppIcon.icns")
	if _, err := os.Stat(appIconIcnsFile); err == nil {
		targetIconPath := filepath.Join(appResourcesPath, "AppIcon.icns")
		if err := copyFile(appIconIcnsFile, targetIconPath); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("替换AppIcon.icns文件失败: %v", err))
		} else {
			utility.LogPrintSuccess("成功替换AppIcon.icns文件")
		}
	}

	// 查找并替换Assets.car文件
	carFile := filepath.Join(buildDir, "Assets.car")
	if _, err := os.Stat(carFile); err == nil {
		targetCarPath := filepath.Join(appResourcesPath, "Assets.car")
		if err := copyFile(carFile, targetCarPath); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("替换Assets.car文件失败: %v", err))
		} else {
			utility.LogPrintSuccess("成功替换Assets.car文件")
		}
	}

	// 8. 合并partial.plist和目标.app下的Info.plist
	partialPlistPath := filepath.Join(buildDir, "partial.plist")
	if _, err := os.Stat(partialPlistPath); err == nil {
		utility.LogPrintInfo("检测到partial.plist文件，准备合并到Info.plist")

		targetInfoPlistPath := filepath.Join(appPath, "Info.plist")
		if _, err := os.Stat(targetInfoPlistPath); err == nil {
			// 调用MergePlistFiles方法合并plist文件
			mergedMap, err := utility.MergePlistFiles(targetInfoPlistPath, partialPlistPath)
			if err != nil {
				utility.LogPrintWarning(fmt.Sprintf("合并partial.plist到Info.plist失败: %v", err))
			} else {
				// 将合并后的内容写回Info.plist
				output, err := plist.MarshalIndent(mergedMap, plist.XMLFormat, "\t")
				if err != nil {
					utility.LogPrintWarning(fmt.Sprintf("序列化合并后的Info.plist失败: %v", err))
				} else {
					err = os.WriteFile(targetInfoPlistPath, output, 0644)
					if err != nil {
						utility.LogPrintWarning(fmt.Sprintf("写入合并后的Info.plist失败: %v", err))
					} else {
						utility.LogPrintSuccess("成功合并partial.plist到Info.plist")
					}
				}
			}
		} else {
			utility.LogPrintWarning(fmt.Sprintf("未找到目标Info.plist文件: %s", targetInfoPlistPath))
		}
	} else {
		utility.LogPrintInfo("未检测到partial.plist文件，跳过合并步骤")
	}

	utility.LogPrintSuccess("应用图标替换完成")
	return nil
}

// replaceLaunchScreen 替换启动图
func replaceLaunchScreen(appPath string, launchScreenPath string, tempDir string) error {
	if launchScreenPath == "" {
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("替换启动图: %s", launchScreenPath))

	// 1. 检查启动图文件是否存在
	if _, err := os.Stat(launchScreenPath); os.IsNotExist(err) {
		return fmt.Errorf("启动图文件不存在: %s", launchScreenPath)
	}

	// 2. 扫描appPath根目录下以LaunchScreen开头，以.png结尾的文件
	utility.LogPrintInfo("扫描现有的启动图文件...")
	var originalLauncherImages []string

	files, err := os.ReadDir(appPath)
	if err != nil {
		return fmt.Errorf("读取应用目录失败: %v", err)
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasPrefix(file.Name(), "LaunchScreen") && strings.HasSuffix(file.Name(), ".png") {
			filePath := filepath.Join(appPath, file.Name())
			originalLauncherImages = append(originalLauncherImages, filePath)
			utility.LogPrintInfo(fmt.Sprintf("找到启动图文件: %s", file.Name()))
		}
	}

	if len(originalLauncherImages) == 0 {
		utility.LogPrintWarning("未找到现有的启动图文件，跳过替换")
		return nil
	}

	// 3. 在config.TempDir路径下，新建一个replaceLauncherImage目录
	replaceLauncherImageDir := filepath.Join(tempDir, "replaceLauncherImage")
	if err := os.MkdirAll(replaceLauncherImageDir, 0755); err != nil {
		return fmt.Errorf("创建replaceLauncherImage目录失败: %w", err)
	}

	// 4. 依次遍历originalLauncherImages里的每一项launcherImage
	for _, launcherImage := range originalLauncherImages {
		// 获取每张图片的大小
		file, err := os.Open(launcherImage)
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("无法打开启动图文件 %s: %v", launcherImage, err))
			continue
		}

		img, _, err := image.DecodeConfig(file)
		file.Close()
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("无法解析启动图文件 %s: %v", launcherImage, err))
			continue
		}

		width := img.Width
		height := img.Height
		fileName := filepath.Base(launcherImage)

		utility.LogPrintInfo(fmt.Sprintf("处理启动图: %s (尺寸: %dx%d)", fileName, width, height))

		// 将config.LaunchScreen强制转成和launcherImage大小和名字一样的图片
		outputPath := filepath.Join(replaceLauncherImageDir, fileName)
		if err := resizeAndSaveImage(launchScreenPath, outputPath, width, height); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("转换启动图失败 %s: %v", fileName, err))
			continue
		}

		utility.LogPrintSuccess(fmt.Sprintf("成功转换启动图: %s", fileName))
	}

	// 5. 调用addFilesToApp方法，将replaceLauncherImage目录内的图片拷贝到appPath根目录下
	if err := addFilesToApp(appPath, replaceLauncherImageDir); err != nil {
		return fmt.Errorf("拷贝启动图文件失败: %w", err)
	}

	utility.LogPrintSuccess("启动图替换完成")
	return nil
}
