package ipa

import (
	"bytes"
	"encoding/json"
	"fmt"
	_ "image/png"
	"onetools/cmd/install"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"howett.net/plist"
)

// ResignResult 重签名结果
type JsonModelResult struct {
	Code       int    `json:"code"`
	Message    string `json:"message,omitempty"`
	LogPath    string `json:"logPath,omitempty"`
	OutputPath string `json:"outputPath,omitempty"`
}

// ToJSON 将结果转换为JSON字符串
func (r *JsonModelResult) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}

// ResignConfig 重签名配置
type ResignConfig struct {
	IPAPath             string
	OutputPath          string
	BundleID            string
	ProvisioningProfile string
	Certificate         string
	CertificateType     string // 证书类型：develop（开发证书）或 distribute（发布证书）
	DisplayName         string
	AppVersion          string
	BuildVersion        string
	AppIconPath         string
	LaunchScreen        string
	TempDir             string
	OriginAppUnzipPath  string // 原IPA解压后的目录路径，位于${TempDir}/unzipOriginApp
	AppName             string
	ForceSwiftUpdate    bool
	EntitlementsPath    string                 // 自定义entitlements.plist文件路径
	KeepTempDir         bool                   // 是否保留临时文件目录，用于调试，默认为false（处理完成后删除临时文件）
	AdditionalPlistPath string                 // 附加的plist文件路径，将合并到app/Info.plist中
	RemovePlistKeys     string                 // 要从Info.plist中删除的最外层key，多个key用逗号分隔
	DeleteFilePaths     string                 // 要从.app目录中删除的文件或文件夹路径，多个路径用逗号分隔
	AddFilesPath        string                 // 要添加到.app目录的文件夹路径，该文件夹内的文件结构对应IPA解压后.app目录的根目录结构
	AppDevConfig        string                 // 请求dev后台App对应的配置文件所需参数，将请求到的配置文件替换到重签后的ipa中。
	DevEntitlementInfo  map[string]interface{} // 解析dev后台的配置，提取出Entitlement的值
	Platform            utility.PlatformOS     //平台类型
}

// InfoPlist Info.plist结构
type InfoPlist struct {
	CFBundleIdentifier         string `plist:"CFBundleIdentifier"`
	CFBundleDisplayName        string `plist:"CFBundleDisplayName"`
	CFBundleShortVersionString string `plist:"CFBundleShortVersionString"`
	CFBundleVersion            string `plist:"CFBundleVersion"`
	CFBundleName               string `plist:"CFBundleName"`
}

// 清理临时目录
func cleanupResign(config *ResignConfig) {
	if config.TempDir != "" && !config.KeepTempDir {
		utility.LogPrintInfo(fmt.Sprintf("清理临时目录: %s", config.TempDir))
		os.RemoveAll(config.TempDir)
	} else if config.KeepTempDir {
		utility.LogPrintInfo(fmt.Sprintf("保留临时目录用于调试: %s", config.TempDir))
	}
}

// 更新Info.plist文件
func updateInfoPlist(plistPath string, config *ResignConfig) error {
	utility.LogPrintInfo(fmt.Sprintf("更新Info.plist文件: %s", plistPath))

	data, err := os.ReadFile(plistPath)
	if err != nil {
		return fmt.Errorf("读取Info.plist失败: %w", err)
	}

	var plistData map[string]interface{}
	_, err = plist.Unmarshal(data, &plistData)
	if err != nil {
		return fmt.Errorf("解析Info.plist失败: %w", err)
	}

	// 更新Bundle ID
	if config.BundleID != "" {
		plistData["CFBundleIdentifier"] = config.BundleID
		utility.LogPrintInfo(fmt.Sprintf("更新Bundle ID: %s", config.BundleID))
	}

	// 更新显示名称
	if config.DisplayName != "" {
		plistData["CFBundleDisplayName"] = config.DisplayName
		plistData["CFBundleName"] = config.DisplayName
		utility.LogPrintInfo(fmt.Sprintf("更新显示名称: %s", config.DisplayName))
	}

	// 更新版本号
	if config.AppVersion != "" {
		plistData["CFBundleShortVersionString"] = config.AppVersion
		utility.LogPrintInfo(fmt.Sprintf("更新应用版本: %s", config.AppVersion))
	}

	// 更新构建版本
	if config.BuildVersion != "" {
		plistData["CFBundleVersion"] = config.BuildVersion
		utility.LogPrintInfo(fmt.Sprintf("更新构建版本: %s", config.BuildVersion))
	}

	// 写回文件
	output, err := plist.MarshalIndent(plistData, plist.XMLFormat, "\t")
	if err != nil {
		return fmt.Errorf("序列化Info.plist失败: %w", err)
	}

	err = os.WriteFile(plistPath, output, 0644)
	if err != nil {
		return fmt.Errorf("写入Info.plist失败: %w", err)
	}

	return nil
}

// processPlistModifications 处理Info.plist文件的修改和合并
// 包括删除指定的key和合并附加的plist文件
func processPlistModifications(plistPath string, config *ResignConfig) error {
	additionalPlistPath := config.AdditionalPlistPath
	removePlistKeys := config.RemovePlistKeys

	// 读取原始的Info.plist文件
	data, err := os.ReadFile(plistPath)
	if err != nil {
		return fmt.Errorf("读取Info.plist失败: %w", err)
	}

	var plistData map[string]interface{}
	_, err = plist.Unmarshal(data, &plistData)
	if err != nil {
		return fmt.Errorf("解析Info.plist失败: %w", err)
	}

	// 处理要删除的key
	if removePlistKeys != "" {
		keysToRemove := strings.Split(removePlistKeys, ",")
		for _, key := range keysToRemove {
			key = strings.TrimSpace(key)
			if key == "" {
				continue
			}

			if _, exists := plistData[key]; exists {
				delete(plistData, key)
				utility.LogPrintInfo(fmt.Sprintf("已删除Info.plist中的key: %s", key))
			} else {
				utility.LogPrintWarning(fmt.Sprintf("Info.plist中不存在key: %s", key))
			}
		}
	}

	// 处理附加的plist文件
	if additionalPlistPath != "" && utility.IsExist(additionalPlistPath) {
		utility.LogPrintInfo(fmt.Sprintf("合并附加的plist文件: %s", additionalPlistPath))

		// 解析附加的plist文件
		additionalData, err := os.ReadFile(additionalPlistPath)
		if err != nil {
			return fmt.Errorf("读取附加的plist文件失败: %w", err)
		}

		var additionalPlistData map[string]interface{}
		_, err = plist.Unmarshal(additionalData, &additionalPlistData)
		if err != nil {
			return fmt.Errorf("解析附加的plist文件失败: %w", err)
		}

		// 合并最外层key
		for key, value := range additionalPlistData {
			plistData[key] = value
			utility.LogPrintInfo(fmt.Sprintf("合并key: %s", key))
		}

		utility.LogPrintSuccess("成功合并附加的plist文件到Info.plist")
	}

	// 从plistData获取新的BundleId，赋值给config
	if bundleID, ok := plistData["CFBundleIdentifier"].(string); ok {
		config.BundleID = bundleID
	}

	// 将修改后的内容写回Info.plist
	output, err := plist.MarshalIndent(plistData, plist.XMLFormat, "\t")
	if err != nil {
		return fmt.Errorf("序列化修改后的Info.plist失败: %w", err)
	}

	err = os.WriteFile(plistPath, output, 0644)
	if err != nil {
		return fmt.Errorf("写入修改后的Info.plist失败: %w", err)
	}

	return nil
}

// 安装mobileprovision文件
func installProvisioningProfile(appPath string, config *ResignConfig) error {
	provisionPath := config.ProvisioningProfile
	//  mobileprovision profile使用顺序： 参数设置的路径或名字，会根据名字自动搜索路径 > 根据设置的certType和bundleId自动查找 > 当前IPA使用的
	// 没设置描述文件，尝试根据cert类型自动获取
	if provisionPath == "" && config.CertificateType != "" {
		if profileType, _ := toProfileType(config.CertificateType); profileType != "" {
			profileInfo, _ := getProfileByBundleIDAndType(config.BundleID, profileType, config.Platform)
			if profileInfo != nil {
				provisionPath = profileInfo.Filename
				utility.LogPrintInfo(fmt.Sprintf("根据类型，自动匹配到mobileprovision文件: %s, 路径: %s", profileInfo.Name, provisionPath))
			}
		}
	}

	if !strings.HasSuffix(provisionPath, ".mobileprovision") && !strings.HasSuffix(provisionPath, ".provisionprofile") {
		//判断设置
		profileInfo, _ := findProfiles(config.Platform, provisionPath)
		if profileInfo != nil {
			provisionPath = profileInfo.Filename
			utility.LogPrintInfo(fmt.Sprintf("根据名字:%s，自动匹配到provision文件路径: %s", profileInfo.Name, provisionPath))
		}
	}

	if provisionPath == "" {
		utility.LogPrintInfo("未获取到mobileprovision文件，将使用当前包内的")
		return nil
	}

	config.ProvisioningProfile = provisionPath
	utility.LogPrintInfo(fmt.Sprintf("安装mobileprovision文件: %s", provisionPath))

	targetPath := filepath.Join(appPath, "embedded.mobileprovision")
	if config.Platform == utility.PlatformOS_OSX {
		targetPath = filepath.Join(appPath, "embedded.provisionprofile")
	}
	err := copyFile(provisionPath, targetPath)
	if err != nil {
		return fmt.Errorf("安装mobileprovision失败: %w", err)
	}

	utility.LogPrintSuccess("成功安装mobileprovision文件")
	return nil
}

// 当provisionPath为空时，尝试从应用目录中获取embedded.mobileprovision文件
func getEmbeddedProvisioningProfile(appPath string, platform utility.PlatformOS) string {
	embeddedFileSuffix := ".mobileprovision"
	if platform == utility.PlatformOS_OSX {
		embeddedFileSuffix = ".provisionprofile"
	}

	embeddedFileName := "embedded" + embeddedFileSuffix
	embeddedProvisionPath := filepath.Join(appPath, embeddedFileName)
	if _, err := os.Stat(embeddedProvisionPath); err == nil {
		utility.LogPrintInfo(fmt.Sprintf("找到embedded.mobileprovision文件: %s", embeddedProvisionPath))
		return embeddedProvisionPath
	}

	// 当获取不到embedded.mobileprovision时，遍历查找当前根目录下的以.mobileprovision结尾的文件
	utility.LogPrintInfo("未找到embedded.mobileprovision，尝试查找其他.mobileprovision文件")
	files, err := os.ReadDir(appPath)
	if err != nil {
		utility.LogPrintWarning(fmt.Sprintf("读取应用目录失败: %v", err))
		return ""
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), embeddedFileSuffix) {
			foundProvisionPath := filepath.Join(appPath, file.Name())
			utility.LogPrintInfo(fmt.Sprintf("找到.mobileprovision文件: %s", foundProvisionPath))
			return foundProvisionPath
		}
	}

	utility.LogPrintWarning("未找到任何.mobileprovision文件")
	return ""
}

// 签名单个文件
func signFile(filePath string, certName string, entitlements string) error {
	args := []string{"-f", "-s", certName}

	// 如果有entitlements文件，添加到参数中
	if entitlements != "" {
		args = append(args, "--entitlements", entitlements)
	}

	args = append(args, filePath)

	cmd := exec.Command("codesign", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("签名文件失败 %s: %s, %w", filePath, string(output), err)
	}
	return nil
}

// 从应用二进制文件中提取entitlements
func extractEntitlementsFromBinary(binaryPath, outputPath string) error {
	utility.LogPrintInfo(fmt.Sprintf("从应用二进制文件提取entitlements: %s", binaryPath))

	// 使用shell命令直接将entitlements写入文件
	// 这样可以避免Go处理输出时可能出现的问题
	shellCmd := fmt.Sprintf("codesign --display --entitlements :- \"%s\" > \"%s\" 2>/dev/null", binaryPath, outputPath)
	cmd := exec.Command("bash", "-c", shellCmd)
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("提取entitlements失败: %w", err)
	}

	// 检查文件是否存在且不为空
	fileInfo, err := os.Stat(outputPath)
	if err != nil || fileInfo.Size() == 0 {
		return fmt.Errorf("提取的entitlements为空或文件不存在")
	}

	// 读取文件内容
	data, err := os.ReadFile(outputPath)
	if err != nil {
		return fmt.Errorf("读取entitlements文件失败: %w", err)
	}

	// 检查是否包含XML头
	if !bytes.Contains(data, []byte("<?xml")) {
		return fmt.Errorf("提取的entitlements不是有效的XML格式")
	}

	// 尝试解析XML
	var entitlements map[string]interface{}
	_, err = plist.Unmarshal(data, &entitlements)
	if err != nil {
		utility.LogPrintWarning(fmt.Sprintf("解析entitlements失败，尝试手动提取关键信息: %v", err))

		// 手动检查是否包含associated-domains
		content := string(data)
		if strings.Contains(content, "com.apple.developer.associated-domains") {
			utility.LogPrintInfo("检测到associated-domains权限，将在合并时特殊处理")
		}
	} else {
		// 检查是否包含associated-domains
		if domains, ok := entitlements["com.apple.developer.associated-domains"]; ok {
			utility.LogPrintInfo(fmt.Sprintf("检测到associated-domains权限: %v", domains))
		}
	}

	utility.LogPrintSuccess(fmt.Sprintf("成功提取entitlements到文件: %s", outputPath))
	return nil
}

// 从mobileprovision文件中提取entitlements并与原应用的entitlements合并
// 当未指定自定义entitlements.plist文件时，使用此函数处理
func extractEntitlements(provisionPath, originAppUnzipPath string, platform utility.PlatformOS) (string, error) {
	utility.LogPrintInfo("检查mobileprovision文件Entitlements")
	appPath := originAppUnzipPath
	if platform == utility.PlatformOS_IOS {
		appPath = filepath.Join(originAppUnzipPath, "Payload")
	}
	// 查找.app目录
	appDirs, err := os.ReadDir(appPath)
	if err != nil {
		return "", fmt.Errorf("读取Payload目录失败: %w", err)
	}

	var appDir string
	for _, dir := range appDirs {
		if dir.IsDir() && strings.HasSuffix(dir.Name(), ".app") {
			appDir = dir.Name()
			break
		}
	}

	if appDir == "" {
		return "", fmt.Errorf("未找到.app目录")
	}
	appBinaryPath := filepath.Join(appPath, appDir)
	appContentPath := appBinaryPath
	if platform == utility.PlatformOS_OSX {
		appContentPath = filepath.Join(appBinaryPath, "Contents")
	}
	// 当provisionPath为空时，尝试从应用目录中获取embedded.mobileprovision文件
	if provisionPath == "" {
		provisionPath = getEmbeddedProvisioningProfile(appContentPath, platform)
		if provisionPath == "" {
			utility.LogPrintWarning("未找到任何mobileprovision文件，将跳过entitlements提取")
			return "", nil
		}
	}

	// 最终输出的entitlements文件路径
	tempDir := filepath.Dir(originAppUnzipPath) // 获取父目录作为临时目录
	entitlementsPath := filepath.Join(tempDir, "entitlements.plist")

	// 从原始应用中提取entitlements
	oldEntitlementsPath := filepath.Join(tempDir, "entitlements_old.plist")

	// 查找应用二进制文件
	var appBinary string
	infoPlistPath := filepath.Join(appContentPath, "Info.plist")
	if _, err := os.Stat(infoPlistPath); err == nil {
		// 读取Info.plist获取可执行文件名
		data, err := os.ReadFile(infoPlistPath)
		if err == nil {
			var plistData map[string]interface{}
			if _, err := plist.Unmarshal(data, &plistData); err == nil {
				if execName, ok := plistData["CFBundleExecutable"].(string); ok {
					if platform == utility.PlatformOS_OSX {
						appBinary = filepath.Join(appContentPath, "MacOS", execName)
					} else {
						appBinary = filepath.Join(appContentPath, execName)
					}
				}
			}
		}
	}

	// 如果无法从Info.plist获取，则使用目录名（去掉.app后缀）
	if appBinary == "" {
		execName := strings.TrimSuffix(appDir, ".app")
		if platform == utility.PlatformOS_OSX {
			appBinary = filepath.Join(appContentPath, "MacOS", execName)
		} else {
			appBinary = filepath.Join(appContentPath, execName)
		}
	}

	// 使用codesign命令从原应用提取entitlements
	utility.LogPrintInfo(fmt.Sprintf("从原应用提取entitlements: %s", appBinary))
	shellCmd := fmt.Sprintf("codesign --display --entitlements :- \"%s\" > \"%s\" 2>/dev/null", appBinary, oldEntitlementsPath)
	cmd := exec.Command("bash", "-c", shellCmd)
	err = cmd.Run()

	var oldEntitlements map[string]interface{}
	if err == nil {
		// 读取提取的entitlements
		oldData, err := os.ReadFile(oldEntitlementsPath)
		if err == nil {
			_, err = plist.Unmarshal(oldData, &oldEntitlements)
			if err != nil {
				utility.LogPrintWarning(fmt.Sprintf("解析原应用entitlements失败: %v", err))

				// 尝试手动解析XML，特别是提取associated-domains
				xmlStr := string(oldData)

				// 创建一个空的entitlements映射
				oldEntitlements = make(map[string]interface{})

				// 查找com.apple.developer.associated-domains部分
				if strings.Contains(xmlStr, "com.apple.developer.associated-domains") {
					utility.LogPrintInfo("找到com.apple.developer.associated-domains节点，尝试手动提取")

					// 提取associated-domains的值
					// 使用正则表达式匹配<key>com.apple.developer.associated-domains</key>后面的<array>...</array>部分
					domainRegex := regexp.MustCompile(`(?s)<key>com\.apple\.developer\.associated-domains</key>\s*<array>(.*?)</array>`)
					matches := domainRegex.FindStringSubmatch(xmlStr)

					if len(matches) > 1 {
						// 提取字符串值
						stringRegex := regexp.MustCompile(`<string>(.*?)</string>`)
						stringMatches := stringRegex.FindAllStringSubmatch(matches[1], -1)

						domains := make([]interface{}, 0)
						for _, match := range stringMatches {
							if len(match) > 1 {
								domains = append(domains, match[1])
								utility.LogPrintInfo(fmt.Sprintf("手动提取到associated-domain: %s", match[1]))
							}
						}

						if len(domains) > 0 {
							oldEntitlements["com.apple.developer.associated-domains"] = domains
							utility.LogPrintInfo(fmt.Sprintf("成功手动提取associated-domains，共 %d 个域名", len(domains)))
						}
					}
				}
			} else {
				utility.LogPrintSuccess("成功从原应用提取entitlements")

				// 调试输出，查看提取的entitlements内容
				for key, value := range oldEntitlements {
					utility.LogPrintInfo(fmt.Sprintf("原应用entitlement: %s = %v", key, value))
				}
			}
		} else {
			utility.LogPrintWarning(fmt.Sprintf("读取原应用entitlements文件失败: %v", err))
			oldEntitlements = make(map[string]interface{})
		}
	} else {
		utility.LogPrintWarning(fmt.Sprintf("从原应用提取entitlements失败: %v", err))
		oldEntitlements = make(map[string]interface{})
	}

	// 从新的mobileprovision提取entitlements
	newEntitlementsPath := filepath.Join(tempDir, "entitlements_new.plist")

	// 使用security命令提取entitlements
	utility.LogPrintInfo(fmt.Sprintf("从mobileprovision提取entitlements: %s", provisionPath))
	newProfileInfo, err := newProvisioningProfile(provisionPath, platform)
	if err != nil {
		return "", fmt.Errorf("提取mobileprovision失败: %w", err)
	}

	newEntitlements := newProfileInfo.Entitlements
	// 将新的entitlements写入文件（用于调试）
	newEntitlementsBytes, _ := plist.MarshalIndent(newEntitlements, plist.XMLFormat, "\t")
	_ = os.WriteFile(newEntitlementsPath, newEntitlementsBytes, 0644)

	// 合并entitlements
	utility.LogPrintInfo("合并entitlements...")
	mergedEntitlements := make(map[string]interface{})

	// 如果原应用有entitlements，以它为基础
	haveGetTaskAllow := false
	if len(oldEntitlements) > 0 {
		// 以原有entitlements中的key为主
		entitlementKeepItemKeys := []string{"com.apple.developer.associated-domains",
			"com.apple.security.app-sandbox",
			"com.apple.security.files.user-selected.read-only",
			"com.apple.security.network.client",
			"com.apple.security.network.server",
		}

		for key, oldValue := range oldEntitlements {
			// 特殊处理，保留原app中的值 com.apple.developer.associated-domains
			if utility.IsContain(entitlementKeepItemKeys, key) {
				// 保留原应用的associated-domains等权限
				mergedEntitlements[key] = oldValue
				utility.LogPrintInfo(fmt.Sprintf("保留原应用的权限: %s = %v", key, oldValue))
			} else if newValue, exists := newEntitlements[key]; exists {
				// 如果新的mobileprovision中也有这个key，使用新值
				mergedEntitlements[key] = newValue
				utility.LogPrintInfo(fmt.Sprintf("使用新mobileprovision的权限: %s = %v", key, newValue))
			} else {
				// 如果新的mobileprovision中没有这个key，则不添加
				utility.LogPrintInfo(fmt.Sprintf("移除权限（新mobileprovision中不存在）: %s", key))
			}

			if key == "get-task-allow" {
				haveGetTaskAllow = true
			}
		}
	} else {
		// 如果原应用没有entitlements，直接使用新的
		mergedEntitlements = newEntitlements
		utility.LogPrintInfo("使用新mobileprovision的所有权限")
	}

	// 判断iOS平台，使用dev证书时，确保get-task-allow=true已添加
	if platform == utility.PlatformOS_IOS && newProfileInfo.Type == ProfileTypeDev && !haveGetTaskAllow {
		mergedEntitlements["get-task-allow"] = true
		utility.LogPrintInfo("主动添加get-task-allow权限: true")
	}

	// 将合并后的entitlements写入文件
	mergedEntitlementsBytes, err := plist.MarshalIndent(mergedEntitlements, plist.XMLFormat, "\t")
	if err != nil {
		return "", fmt.Errorf("序列化合并后的entitlements失败: %w", err)
	}

	err = os.WriteFile(entitlementsPath, mergedEntitlementsBytes, 0644)
	if err != nil {
		return "", fmt.Errorf("写入合并后的entitlements文件失败: %w", err)
	}

	utility.LogPrintSuccess(fmt.Sprintf("成功生成合并后的entitlements文件: %s", entitlementsPath))
	return entitlementsPath, nil
}

// getCurrentAppCertificate 获取当前.app使用的证书名称
func getCurrentAppCertificate(appContentPath string, platform utility.PlatformOS) (string, error) {
	appPath := appContentPath
	if platform == utility.PlatformOS_OSX {
		appPath = filepath.Dir(appContentPath)
	}
	utility.LogPrintInfo(fmt.Sprintf("获取当前.app的证书信息: %s", appPath))

	// 使用codesign命令获取签名信息
	cmd := exec.Command("codesign", "-dvvv", appPath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("获取签名信息失败: %s, %w", string(output), err)
	}

	outputStr := string(output)
	utility.LogPrintInfo(fmt.Sprintf("签名信息: %s", outputStr))

	// 解析输出，查找Authority行
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Authority=") {
			// 提取证书名称
			certName := strings.TrimPrefix(line, "Authority=")
			utility.LogPrintInfo(fmt.Sprintf("找到证书: %s", certName))
			return certName, nil
		}
	}

	return "", fmt.Errorf("未找到证书信息")
}

// 签名应用和动态库
func signApp(appContentRootPath string, config *ResignConfig) error {
	// 获取证书名称
	// 1. 设置的证书 > 根据mobileprovision自动搜索 > 当前IPA使用的
	certName := ""
	if config.Certificate != "" {
		// 直接使用传入的证书名称
		certName = config.Certificate
		utility.LogPrintInfo(fmt.Sprintf("使用指定的证书: %s", certName))
	} else if config.ProvisioningProfile != "" {
		// 根据证书类型自动选择证书
		selectedCert, err := MatchCertificateFromProfile(config.ProvisioningProfile)
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("根据mobileprovision profile自动查找证书失败: %v", err))
		} else {
			certName = selectedCert.IdentityName
			utility.LogPrintInfo(fmt.Sprintf("根据mobileprovision profile自动查找证书的证书: %s", certName))
		}
	}

	if certName == "" {
		// 如果没有指定证书和证书类型，尝试获取当前.app使用的证书
		currentCert, err := getCurrentAppCertificate(appContentRootPath, config.Platform)
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("获取当前.app证书失败: %v", err))
			return err
		} else {
			certName = currentCert
			utility.LogPrintInfo(fmt.Sprintf("使用当前.app的证书: %s", certName))
		}
	}
	// 保存回config.Certificate，方便后续使用
	config.Certificate = certName

	// 获取entitlements
	var entitlementsPath string
	if config.EntitlementsPath != "" {
		// 使用外部传入的entitlements文件
		entitlementsPath = config.EntitlementsPath
		utility.LogPrintInfo(fmt.Sprintf("使用外部传入的entitlements文件: %s", entitlementsPath))
	} else {
		// 从原应用和mobileprovision中提取并合并entitlements
		extractedPath, err := extractEntitlements(config.ProvisioningProfile, config.OriginAppUnzipPath, config.Platform)
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("提取和合并entitlements失败: %v", err))
		} else {
			entitlementsPath = extractedPath
			utility.LogPrintInfo(fmt.Sprintf("成功提取和合并entitlements: %s", entitlementsPath))
		}
	}

	// 查看是否有从dev后台获取的配置，有则和entitlementsPath进行合并
	if _, error := mergeEntitlements(entitlementsPath, config.DevEntitlementInfo); error != nil {
		return error
	}

	// 先删除原有签名文件_CodeSignature目录
	codeSignaturePath := filepath.Join(appContentRootPath, "_CodeSignature")
	if _, err := os.Stat(codeSignaturePath); err == nil {
		utility.LogPrintInfo(fmt.Sprintf("删除原有签名目录: %s", codeSignaturePath))
		if err := os.RemoveAll(codeSignaturePath); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("删除原有签名目录失败: %v", err))
		} else {
			utility.LogPrintSuccess("成功删除原有签名目录")
		}
	}

	// 首先签名Frameworks目录中的动态库和Framework
	appPath := appContentRootPath
	if utility.PlatformOS_OSX == config.Platform {
		appPath = filepath.Dir(appContentRootPath)
	}

	dylibFiles, _ := scanCustomLibraries(appPath, config.Platform)

	// 先签名独立的Framework 和 .dylib文件
	for _, dylibPath := range dylibFiles {
		utility.LogPrintInfo(fmt.Sprintf("签名动态库: %s", filepath.Base(dylibPath)))
		dylibFullPath := filepath.Join(appPath, dylibPath)
		if err := signFile(dylibFullPath, certName, ""); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("签名动态库失败 %s: %v", filepath.Base(dylibPath), err))
		} else {
			utility.LogPrintSuccess(fmt.Sprintf("成功签名动态库: %s", filepath.Base(dylibPath)))
		}
	}

	// 最后签名主应用
	utility.LogPrintInfo(fmt.Sprintf("签名主应用: %s", config.AppName))
	if err := signFile(appPath, certName, entitlementsPath); err != nil {
		return fmt.Errorf("%w", err)
	}

	utility.LogPrintSuccess("成功签名主应用")

	// 验证签名（对.app进行验证而不是.ipa）
	utility.LogPrintInfo("验证应用签名...")
	cmd := exec.Command("codesign", "-dvvv", appPath)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("签名验证失败: %s, %w", string(output), err)
	} else {
		utility.LogPrintInfo(fmt.Sprintf("签名验证通过,应用签名信息: %s", string(output)))
	}

	// 显示签名信息
	cmd = exec.Command("codesign", "-d", "--entitlements", "-", appPath)
	if output, err := cmd.CombinedOutput(); err == nil {
		utility.LogPrintInfo(fmt.Sprintf("entitlements签名信息: %s", string(output)))
	}

	return nil
}

// 检查是否需要处理SwiftSupport
func needHandleSwiftSupport(config *ResignConfig) bool {
	// 检查是否存在SwiftSupport目录
	swiftSupportPath := filepath.Join(config.OriginAppUnzipPath, "SwiftSupport")
	hasSwiftSupport := false
	if _, err := os.Stat(swiftSupportPath); err == nil {
		hasSwiftSupport = true
		utility.LogPrintInfo("检测到原IPA包中包含SwiftSupport目录")
	}

	// 如果有强制更新参数或者存在SwiftSupport目录，则需要处理
	if config.ForceSwiftUpdate || hasSwiftSupport {
		return true
	}

	utility.LogPrintInfo("跳过SwiftSupport处理（未设置强制更新且未检测到SwiftSupport目录）")
	return false
}

// 处理SwiftSupport（如果需要）
func handleSwiftSupport(config *ResignConfig) error {
	if !needHandleSwiftSupport(config) {
		return nil
	}

	utility.LogPrintInfo("处理SwiftSupport...")

	// 创建临时的SwiftSupport配置
	swiftConfig := &Config{
		IPAPath:      filepath.Join(config.TempDir, "temp.ipa"),
		TempDir:      config.OriginAppUnzipPath, // 使用原IPA解压目录作为工作目录
		AppName:      config.AppName,
		ForceUpdate:  true,
		ToolchainDir: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib",
		NeedUpdate:   true,
	}

	return processSwiftSupport(swiftConfig)
}

// 创建重签名后的IPA
func createResignedIPA(config *ResignConfig) (string, error) {
	var outputPath string
	outputDirPath := config.OutputPath
	if outputDirPath == "" {
		outputDirPath = filepath.Dir(config.IPAPath)
	}
	timeStr := time.Now().Format("20060102_150405")
	if config.Platform == utility.PlatformOS_OSX {
		outputPath = filepath.Join(outputDirPath, fmt.Sprintf("%s_resigned_%s.zip", config.AppName, timeStr))
	} else {
		outputPath = filepath.Join(outputDirPath, fmt.Sprintf("%s_resigned_%s.ipa", config.AppName, timeStr))
	}

	utility.LogPrintInfo(fmt.Sprintf("创建重签名IPA: %s", outputPath))

	// 直接调用createZipIPA方法进行压缩
	if err := createZipIPA(config, outputPath); err != nil {
		return "", fmt.Errorf("创建IPA文件失败: %w", err)
	}

	// 如果zip命令创建IPA成功，直接返回
	// utility.LogPrintSuccess(fmt.Sprintf("重签名IPA创建完成: %s", outputPath))
	return outputPath, nil
}

// 使用zip命令创建IPA
func createZipIPA(config *ResignConfig, outputPath string) error {
	// 删除已存在的输出文件
	if _, err := os.Stat(outputPath); err == nil {
		if err := os.Remove(outputPath); err != nil {
			return fmt.Errorf("删除已存在的输出文件失败: %w", err)
		}
	}

	if config.Platform == utility.PlatformOS_OSX && strings.HasSuffix(outputPath, ".app") {
		// 不需要压缩成zip，目前macOS .app还支持.zip文件传入
		var unArchiveAppFilePath string
		entries, err := os.ReadDir(config.OriginAppUnzipPath)
		if err != nil {
			return fmt.Errorf("读取解压目录失败: %w", err)
		}

		// 查找以.app结尾的文件（实际是文件夹）
		for _, entry := range entries {
			if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
				unArchiveAppFilePath = filepath.Join(config.OriginAppUnzipPath, entry.Name())
				utility.LogPrintInfo(fmt.Sprintf("找到.app文件: %s", unArchiveAppFilePath))
				break
			}
		}

		if unArchiveAppFilePath == "" {
			return fmt.Errorf("未找到.app文件")
		}

		// 将.app文件及其内部子文件按原有格式拷贝到outputPath
		utility.LogPrintInfo(fmt.Sprintf("拷贝.app文件到输出路径: %s -> %s", unArchiveAppFilePath, outputPath))
		if err := copyDirRecursively(unArchiveAppFilePath, outputPath); err != nil {
			return fmt.Errorf("拷贝.app文件失败: %w", err)
		}

		utility.LogPrintSuccess(fmt.Sprintf(".app文件拷贝完成: %s", outputPath))

	} else {
		utility.LogPrintInfo(fmt.Sprintf("使用zip命令创建IPA/ZIP文件: %s", outputPath))
		// 获取最顶层目录名用于日志
		var topLevelItems []string
		entries, err := os.ReadDir(config.OriginAppUnzipPath)
		if err != nil {
			return fmt.Errorf("读取解压目录失败: %w", err)
		}

		for _, entry := range entries {
			if entry.Name() != "__MACOSX" && !strings.HasPrefix(entry.Name(), ".") {
				topLevelItems = append(topLevelItems, entry.Name())
			}
		}

		topLevelDir := strings.Join(topLevelItems, " ")
		utility.LogPrintInfo(fmt.Sprintf("开始压缩文件(%s)", topLevelDir))

		// 统计压缩前的文件大小
		var totalSize int64
		err = filepath.Walk(config.OriginAppUnzipPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() {
				totalSize += info.Size()
			}
			return nil
		})
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("统计文件大小失败: %v", err))
		} else {
			utility.LogPrintInfo(fmt.Sprintf("压缩前总文件大小: %.2f MB", float64(totalSize)/(1024*1024)))
		}

		utility.LogPrintInfo("正在压缩，时间可能较长，请等待...")
		// 使用zip命令压缩，-r表示递归，-q表示安静模式 -y (store symlinks as symlinks)将符号链接存储为链接而不是引用的文件
		args := append([]string{"-r", "-q", "-y", outputPath}, strings.Fields(topLevelDir)...)
		cmd := exec.Command("zip", args...)
		cmd.Dir = config.OriginAppUnzipPath

		output, err := cmd.CombinedOutput()
		if err != nil {
			return fmt.Errorf("zip命令执行失败: %s, %w", string(output), err)
		}

		// 检查输出文件是否创建成功并显示大小
		if stat, err := os.Stat(outputPath); err != nil {
			return fmt.Errorf("IPA/ZIP文件创建失败: %w", err)
		} else {
			outputSize := float64(stat.Size()) / (1024 * 1024)
			utility.LogPrintSuccess(fmt.Sprintf("IPA/ZIP文件压缩完成: %s (大小: %.2f MB)", outputPath, outputSize))
		}
	}
	return nil
}

// ResignIPAExe 执行IPA重签名
func ResignIPAExe(config *ResignConfig) (string, error) {
	// 确保是绝对路径
	if !filepath.IsAbs(config.IPAPath) {
		absPath, err := filepath.Abs(config.IPAPath)
		if err != nil {
			return "", fmt.Errorf("无法获取IPA/APP文件的绝对路径: %w", err)
		}
		config.IPAPath = absPath
	}

	// 设置临时目录
	timestamp := time.Now().Format("20060102_150405.000")
	resignTemp := fmt.Sprintf("resignTemp_%s", timestamp)
	config.TempDir = filepath.Join(filepath.Dir(config.IPAPath), resignTemp)
	// 设置原IPA解压目录
	config.OriginAppUnzipPath = filepath.Join(config.TempDir, "unzipOriginApp")
	defer cleanupResign(config)

	// 清理并创建临时目录
	utility.LogPrintInfo(fmt.Sprintf("清理并创建临时目录: %s", config.TempDir))
	if err := os.RemoveAll(config.TempDir); err != nil {
		return "", fmt.Errorf("无法清理临时目录: %w", err)
	}
	if err := os.MkdirAll(config.TempDir, 0755); err != nil {
		return "", fmt.Errorf("无法创建临时目录: %w", err)
	}

	// 创建原IPA解压目录
	utility.LogPrintInfo(fmt.Sprintf("创建原IPA解压目录: %s", config.OriginAppUnzipPath))
	if err := os.MkdirAll(config.OriginAppUnzipPath, 0755); err != nil {
		return "", fmt.Errorf("无法创建原IPA解压目录: %w", err)
	}

	ipaName := filepath.Base(config.IPAPath)
	if config.Platform == utility.PlatformOS_IOS ||
		(config.Platform == utility.PlatformOS_OSX && strings.HasSuffix(ipaName, ".zip")) {
		// 复制并解压IPA文件
		utility.LogPrintInfo("复制并解压包文件...")
		tempTargetIpaPath := filepath.Join(config.TempDir, ipaName)
		defer os.Remove(tempTargetIpaPath)

		if err := copyFile(config.IPAPath, tempTargetIpaPath); err != nil {
			return "", fmt.Errorf("无法复制包文件: %w", err)
		}

		if err := UnzipIPAExe(tempTargetIpaPath, config.OriginAppUnzipPath); err != nil {
			return "", fmt.Errorf("解压包文件失败: %w", err)
		}
	} else if config.Platform == utility.PlatformOS_OSX && strings.HasSuffix(ipaName, ".pkg") {
		// macOS支持传入pkg包，将pkg包解压到config.OriginAppUnzipPath目录
		utility.LogPrintInfo("检测到pkg包，开始解压...")
		if err := UnzipPkgExe(config.IPAPath, config.OriginAppUnzipPath); err != nil {
			return "", fmt.Errorf("解压pkg包失败: %w", err)
		}
	} else {
		// .app文件直接拷贝到temp目录
		// copyDirRecursivelyForAdd(ipaPath, tempDir)
		tempDir := filepath.Join(config.OriginAppUnzipPath, ipaName)
		copyDirRecursively(config.IPAPath, tempDir)
	}

	// 获取.app应用名称 和 .app文件路径
	// 注意：appContentRootPath: iOS返回.app文件路径，macOS返回.app/Contents路径
	appName, appContentRootPath, err := getAppFileNameAndContentRootPath(config.OriginAppUnzipPath, config.Platform)
	if err != nil {
		return "", fmt.Errorf("获取可执行文件名称失败: %w", err)
	}
	config.AppName = appName
	utility.LogPrintInfo(fmt.Sprintf("可执行文件名称: %s", config.AppName))

	// 更新Info.plist
	plistPath := filepath.Join(appContentRootPath, "Info.plist")
	if err := updateInfoPlist(plistPath, config); err != nil {
		return "", fmt.Errorf("更新Info.plist失败: %w", err)
	}

	// 通过判断AppDevConfig是否为空，来决定是否需要从dev后台请求初始化参数
	if downloadConfigPath, err := handleRequestAppDevConfig(config); err != nil {
		return "", fmt.Errorf("处理从Dev后台下载配置失败: %w", err)
	} else {
		if downloadConfigPath != "" {
			// 处理从后台拉取的配置文件，主要会添加配置文件、更新Info.plist和entitlement
			if err := handleDownloadDevConfig(config, downloadConfigPath, appContentRootPath); err != nil {
				return "", fmt.Errorf("处理AppDevConfig失败: %w", err)
			}
		}
	}

	// 处理Info.plist文件的修改和合并
	if err := processPlistModifications(plistPath, config); err != nil {
		return "", fmt.Errorf("处理Info.plist修改失败: %w", err)
	}

	// 删除指定的文件或文件夹
	if err := deleteFilesFromApp(appContentRootPath, config.DeleteFilePaths); err != nil {
		return "", fmt.Errorf("删除文件失败: %w", err)
	}

	// 添加指定文件夹中的文件到.app目录
	if err := addFilesToApp(appContentRootPath, config.AddFilesPath); err != nil {
		return "", fmt.Errorf("添加文件失败: %w", err)
	}

	// 安装mobileprovision文件
	if err := installProvisioningProfile(appContentRootPath, config); err != nil {
		return "", fmt.Errorf("安装mobileprovision失败: %w", err)
	}

	// 替换应用图标
	if config.Platform == utility.PlatformOS_OSX {
		if err := replaceOSXAppIcon(appContentRootPath, config.AppIconPath, config.TempDir); err != nil {
			return "", fmt.Errorf("替换应用图标失败: %w", err)
		}
	} else {
		if err := replaceAppIcon(appContentRootPath, config.AppIconPath, config.TempDir); err != nil {
			return "", fmt.Errorf("替换应用图标失败: %w", err)
		}
	}

	// 替换启动图
	if err := replaceLaunchScreen(appContentRootPath, config.LaunchScreen, config.TempDir); err != nil {
		return "", fmt.Errorf("替换启动图失败: %w", err)
	}

	// 在签名前检查rpath配置
	if err := checkAndFixRpathBeforeSign(config); err != nil {
		return "", fmt.Errorf("检查rpath配置失败: %w", err)
	}

	// 重新签名
	if err := signApp(appContentRootPath, config); err != nil {
		return "", fmt.Errorf("%w", err)
	}

	// 处理各平台特有流程，iOS添加SwiftSupport、macOS判断是否要打成pkg
	if config.Platform == utility.PlatformOS_IOS {
		needSwiftSupport := needHandleSwiftSupport(config)
		if needSwiftSupport {
			if err := handleSwiftSupport(config); err != nil {
				return "", fmt.Errorf("处理SwiftSupport失败: %w", err)
			}
		}
	} else if config.Platform == utility.PlatformOS_OSX {
		if strings.Contains(config.Certificate, "Apple Distribution") || strings.Contains(config.Certificate, "3rd Party Mac Developer Application") {
			// macOS app发布包，自动打包成pkg格式，方便上传Appstore
			if err := createMacOSPkg(config); err != nil {
				utility.LogPrintError(fmt.Sprintf("创建pkg包失败: %v", err))
			}
		}
	}

	// 创建重签名后的IPA
	if outputPath, err := createResignedIPA(config); err != nil {
		return "", fmt.Errorf("创建重签名IPA/App失败: %w", err)
	} else {
		utility.LogPrintSuccess(fmt.Sprintf("IPA/App重签名完成,路径: %s", outputPath))
		return outputPath, nil
	}
}

// checkAndFixRpathBeforeSign 在签名前检查和修复rpath配置
func checkAndFixRpathBeforeSign(config *ResignConfig) error {
	utility.LogPrintInfo("检查Frameworks中动态库的rpath配置...")

	// 查找.app文件
	appPath, _ := findAppDirectory(config.OriginAppUnzipPath, config.Platform)

	// 分析rpath信息
	rpathInfo, err := analyzeRpathInfo(appPath, config.Platform)
	if err != nil {
		return fmt.Errorf("分析rpath信息失败: %w", err)
	}

	// 检查是否有未正确加载的动态库
	if len(rpathInfo.UnloadedLibraries) > 0 {
		utility.LogPrintWarning(fmt.Sprintf("发现 %d 个未被主进程加载的动态库:", len(rpathInfo.UnloadedLibraries)))
		for i, lib := range rpathInfo.UnloadedLibraries {
			utility.LogPrintInfo(fmt.Sprintf("   %d. %s", i+1, lib))
		}

		// 自动修复未加载的动态库
		utility.LogPrintInfo("自动修复未加载的动态库...")
		if err := fixUnloadedLibraries(rpathInfo); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("修复未加载动态库失败: %v", err))
		}
	} else {
		utility.LogPrintSuccess("所有Frameworks中的动态库都已被主进程正确加载")
	}

	// 检查是否有缺失的rpath配置
	if len(rpathInfo.MissingRpaths) > 0 {
		utility.LogPrintWarning(fmt.Sprintf("发现 %d 个缺失的rpath配置:", len(rpathInfo.MissingRpaths)))
		for i, rpath := range rpathInfo.MissingRpaths {
			utility.LogPrintInfo(fmt.Sprintf("   %d. %s", i+1, rpath))
		}

		// 自动修复rpath配置
		utility.LogPrintInfo("自动修复缺失的rpath配置...")
		if err := fixRpathConfiguration(rpathInfo); err != nil {
			return fmt.Errorf("修复rpath配置失败: %w", err)
		}
		utility.LogPrintSuccess("rpath配置修复完成")
	} else {
		utility.LogPrintSuccess("rpath配置检查通过，无需修复")
	}

	return nil
}

// handleAppDevConfig 处理AppDevConfig参数，从dev后台获取配置文件
func handleRequestAppDevConfig(config *ResignConfig) (string, error) {
	// 如果AppDevConfig为空，跳过处理
	if config.AppDevConfig == "" {
		utility.LogPrintInfo("AppDevConfig为空，跳过dev后台配置文件获取")
		return "", nil
	}

	utility.LogPrintInfo(fmt.Sprintf("处理AppDevConfig参数: %s", config.AppDevConfig))

	// 在config.TempDir里新建appDevConfig目录
	appDevConfigDir := filepath.Join(config.TempDir, "appDevConfig")
	if err := os.MkdirAll(appDevConfigDir, 0755); err != nil {
		return "", fmt.Errorf("创建appDevConfig目录失败: %w", err)
	}

	// 解析appDevConfig的值，先按逗号分隔，再将每一段按=分隔
	requestInfo := make(map[string]string)
	segments := strings.Split(config.AppDevConfig, ",")
	for _, segment := range segments {
		segment = strings.TrimSpace(segment)
		if segment == "" {
			continue
		}

		parts := strings.SplitN(segment, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			requestInfo[key] = value
			utility.LogPrintInfo(fmt.Sprintf("解析参数: %s = %s", key, value))
		}
	}

	// 检查host参数
	hostUrl, hasHost := requestInfo["host"]
	if !hasHost {
		hostUrl = ""
	}

	// 调用load_config_file.go文件里的FetchProjectConfig方法
	utility.SetPlatformOS(utility.PlatformOS_IOS)
	err := install.FetchProjectConfig(
		appDevConfigDir, // ProjectPath传入新建的appDevConfig路径
		appDevConfigDir, // DownloadPath传入新建的appDevConfig路径
		requestInfo,     // requestParams传入requestInfo
		config.Platform, // platformOS
		hostUrl,         // devHostUrl传入requestInfo[host]的值
		"0",             // engineValue传入0
	)

	if err != nil {
		return "", fmt.Errorf("获取dev后台配置文件失败: %w", err)
	}

	utility.LogPrintSuccess("成功获取dev后台配置文件")
	// 下载后NativeSDK配置文件存储的路径
	nativeSDKDirName := utility.GetNativeSDKName()
	nativeSDKConfigDir := filepath.Join(appDevConfigDir, nativeSDKDirName)
	return nativeSDKConfigDir, nil
}

// createMacOSPkg 创建macOS pkg包
func createMacOSPkg(config *ResignConfig) error {
	utility.LogPrintInfo("开始创建macOS pkg包...")

	// 1. 提取team标识，将证书以:分隔，取第一项的值，并移除两端空格
	var teamIdentifier string
	certParts := strings.Split(config.Certificate, ":")
	if len(certParts) > 0 {
		teamIdentifier = strings.TrimSpace(certParts[1])
	}
	if teamIdentifier == "" {
		return fmt.Errorf("无法从证书中提取team标识: %s", config.Certificate)
	}

	utility.LogPrintInfo(fmt.Sprintf("提取到team标识: %s", teamIdentifier))

	// 2. 获取3rd Party Mac Developer Installer证书
	installerCert, err := getLocal3rdPartyMacDeveloperInstallerCert(teamIdentifier)
	if err != nil {
		return fmt.Errorf("获取3rd Party Mac Developer Installer证书失败: %w", err)
	}
	utility.LogPrintInfo(fmt.Sprintf("找到Installer证书: %s", installerCert.IdentityName))

	// 3. 查找.app文件
	var unArchiveAppFilePath string
	entries, err := os.ReadDir(config.OriginAppUnzipPath)
	if err != nil {
		return fmt.Errorf("读取解压目录失败: %w", err)
	}

	// 查找以.app结尾的文件（实际是文件夹）
	for _, entry := range entries {
		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			unArchiveAppFilePath = filepath.Join(config.OriginAppUnzipPath, entry.Name())
			utility.LogPrintInfo(fmt.Sprintf("找到.app文件: %s", unArchiveAppFilePath))
			break
		}
	}

	if unArchiveAppFilePath == "" {
		return fmt.Errorf("未找到.app文件")
	}

	// 4. 执行pkg打包
	pkgOutputPath := strings.TrimSuffix(unArchiveAppFilePath, ".app") + ".pkg"
	if err := buildPkgWithProductbuild(unArchiveAppFilePath, pkgOutputPath, installerCert.IdentityName); err != nil {
		return fmt.Errorf("执行pkg打包失败: %w", err)
	}

	utility.LogPrintSuccess(fmt.Sprintf("成功创建pkg包: %s", pkgOutputPath))
	return nil
}

// buildPkgWithProductbuild 使用productbuild命令将.app打包成pkg
func buildPkgWithProductbuild(appPath, pkgOutputPath, installerCertName string) error {
	utility.LogPrintInfo(fmt.Sprintf("使用productbuild将.app打包成pkg..."))
	utility.LogPrintInfo(fmt.Sprintf("输入: %s", appPath))
	utility.LogPrintInfo(fmt.Sprintf("输出: %s", pkgOutputPath))
	utility.LogPrintInfo(fmt.Sprintf("证书: %s", installerCertName))

	// 构建productbuild命令
	// productbuild --component /path/to/App.app /Applications --sign "3rd Party Mac Developer Installer: Team Name (TEAMID)" /path/to/output.pkg
	cmd := exec.Command("productbuild",
		"--component", appPath, "/Applications",
		"--sign", installerCertName,
		pkgOutputPath)

	// 执行命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("productbuild命令执行失败: %s, 错误: %w", string(output), err)
	}

	utility.LogPrintInfo(fmt.Sprintf("productbuild输出: %s", string(output)))

	// 验证签名pkg签名
	utility.LogPrintInfo("验证pkg签名...")
	cmd = exec.Command("pkgutil", "--check-signature", pkgOutputPath)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("pkg签名验证失败: %s, %w", string(output), err)
	} else {
		utility.LogPrintInfo(fmt.Sprintf("pkg签名验证通过,应用签名信息: %s", string(output)))
	}

	return nil
}
