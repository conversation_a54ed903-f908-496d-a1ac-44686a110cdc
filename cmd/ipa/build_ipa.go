package ipa

import (
	"fmt"
	"onetools/cmd/utility"
	"onetools/cmd/xcode"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"

	"howett.net/plist"
)

// BuildConfig 构建配置结构体
type BuildConfig struct {
	ProjectPath         string             // 项目路径
	Workspace           string             // workspace文件名
	Project             string             // project文件名
	Scheme              string             // 构建scheme
	Configuration       string             // 构建配置，默认Release
	OutputPath          string             // 输出路径
	BundleID            string             // Bundle ID
	ProvisioningProfile string             // ProvisioningProfile
	CertificateType     string             // 证书类型
	Platform            utility.PlatformOS //iOS、macOS平台类型
	XcodeBuildArgs      string             // 追加到xcodebuild archive命令的额外参数
	AddFilesPath        string             // 要添加到.app目录的文件夹路径，该文件夹内的文件结构对应IPA解压后.app目录的根目录结构
	IsUnityProject      bool               // 是否为unity项目
	AppDevConfig        string             // 请求dev后台App对应的配置文件所需参数，将请求到的配置文件替换到重签后的ipa中。
}

// ProjectSettings 项目设置结构体
type ProjectSettings struct {
	ProductBundleIdentifier      string // PRODUCT_BUNDLE_IDENTIFIER
	ProvisioningProfileSpecifier string // PROVISIONING_PROFILE_SPECIFIER
	DevelopmentTeam              string // DEVELOPMENT_TEAM
	CodeSignIdentity             string // CODE_SIGN_IDENTITY
	TargetName                   string // TARGET_NAME
	ARCHS                        string // ARCHS
	OTHER_LDFLAGS                string // OTHER_LDFLAGS
	CodeSignEntitlements         string // CODE_SIGN_ENTITLEMENTS
	InfoPlistFile                string // INFOPLIST_FILE
}

// ExportOptions 项目设置结构体
type ExportOptions struct {
	ProductBundleIdentifier      string      // PRODUCT_BUNDLE_IDENTIFIER
	ProvisioningProfileSpecifier string      // PROVISIONING_PROFILE_SPECIFIER
	DevelopmentTeam              string      // DEVELOPMENT_TEAM
	CodeSignIdentity             string      // CODE_SIGN_IDENTITY
	ProfileType                  ProfileType // 描述文件类型
	ARCHS                        string      // ARCHS
	ProvisioningProfilePath      string      // PROVISIONING_PROFILE_PATH
}

// BuildIPAExe 构建iOS项目生成ipa文件 (新版本)
func BuildIPAExe(config *BuildConfig) (string, error) {
	utility.LogPrintInfo("开始构建XCode项目...")
	utility.LogPrintInfo(fmt.Sprintf("项目路径: %s", config.ProjectPath))

	// 设置默认configuration
	if config.Configuration == "" {
		config.Configuration = "Release"
	}
	// 确定输出路径
	exportPath := filepath.Join(config.ProjectPath, "build")
	if config.OutputPath != "" {
		exportPath = utility.GetDir(config.OutputPath)
	}
	config.OutputPath = exportPath
	if err := os.RemoveAll(exportPath); err != nil {
		return "", fmt.Errorf("删除原有build目录失败: %v", err)
	}

	// 创建build目录
	if err := os.MkdirAll(exportPath, 0755); err != nil {
		return "", fmt.Errorf("创建build目录失败: %w", err)
	}

	// 1. 验证项目路径
	if err := validateXCodeProjectPath(config.ProjectPath); err != nil {
		return "", fmt.Errorf("项目路径验证失败: %w", err)
	}

	// 2. 确定project_file_param
	projectFileParam, err := determineProjectFileParam(config)
	if err != nil {
		return "", fmt.Errorf("确定项目文件参数失败: %w", err)
	}
	utility.LogPrintInfo(fmt.Sprintf("项目文件参数: %s", projectFileParam))

	// 3. 确定scheme
	finalScheme, err := determineScheme(projectFileParam, config)
	if err != nil {
		return "", err
	}
	config.Scheme = finalScheme
	utility.LogPrintInfo(fmt.Sprintf("使用scheme: %s", finalScheme))

	// 4. 获取项目构建设置
	projectSettings, err := getProjectBuildSettings(projectFileParam, finalScheme, config.Configuration, config.ProjectPath)
	if err != nil {
		return "", err
	}

	// 5. 修改项目配置，确保PRODUCT_BUNDLE_IDENTIFIER、PROVISIONING_PROFILE_SPECIFIER、DEVELOPMENT_TEAM等信息设置正确
	exportOptions, err := validateAndUpdateBuildSettings(config, projectSettings)
	if err != nil {
		return "", err
	}

	// 6. 判断是否为Unity项目，Unity项目时判断UnityFramework Target的OTHER_LDFLAGS是否添加了-ObjC设置
	if err := handleUnityFrameworkBuildSettings(config); err != nil {
		return "", fmt.Errorf("处理Unity项目OTHER_LDFLAGS设置失败: %w", err)
	}

	// 为更新Info.plist、entitlement等配置，构建ResignConfig，共用重签名流程完成配置修改
	resignConfig := &ResignConfig{
		TempDir:             exportPath,
		AppDevConfig:        config.AppDevConfig,
		Platform:            config.Platform,
		ProvisioningProfile: exportOptions.ProvisioningProfilePath,
		AddFilesPath:        config.AddFilesPath,
	}

	// 7. 判断是否需要dev后台下载配置，并合并到Info.plist和Entitlement文件中
	if err := handleBuildAppDevConfig(config, resignConfig, projectSettings); err != nil {
		return "", err
	}

	// 8. 执行clean
	_ = executeCleanWithConfig(projectFileParam, config)

	// 9. 执行build archive
	xcarchivePath, err := executeBuildWithConfig(projectFileParam, finalScheme, config, exportOptions)
	if err != nil {
		return "", fmt.Errorf("执行build archive失败: %w", err)
	}

	// 10. 添加从dev后台拉取的配置文件，如OneSDKiOSConfigFile.config、globalSDKConfig.plist等
	if err := handleBuildAdditionalFile(config, xcarchivePath, resignConfig); err != nil {
		return "", err
	}

	// 11. 执行build exportArchive
	if err := exportIPAWithConfig(config, exportOptions); err != nil {
		return "", fmt.Errorf("执行build  exportArchive失败: %w", err)
	}

	return config.OutputPath, nil
}

// validateXCodeProjectPath 验证项目路径是否有效
func validateXCodeProjectPath(projectPath string) error {
	utility.LogPrintInfo("验证项目路径...")

	// 检查路径是否存在
	if !utility.IsExist(projectPath) {
		return fmt.Errorf("项目路径不存在: %s", projectPath)
	}

	// 检查是否为目录
	fileInfo, err := os.Stat(projectPath)
	if err != nil {
		return fmt.Errorf("无法获取路径信息: %w", err)
	}

	if !fileInfo.IsDir() {
		return fmt.Errorf("项目路径不是目录: %s", projectPath)
	}

	// 使用find命令查找.xcodeproj文件
	cmd := exec.Command("find", projectPath, "-maxdepth", "1", "-name", "*.xcodeproj", "-type", "d")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("搜索.xcodeproj文件时出错: %w", err)
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" {
		return fmt.Errorf("在项目路径下未找到.xcodeproj文件: %s", projectPath)
	}

	// 输出找到的.xcodeproj文件
	xcodeprojs := strings.Split(outputStr, "\n")
	for _, xcodeproj := range xcodeprojs {
		if xcodeproj != "" {
			utility.LogPrintInfo(fmt.Sprintf("找到.xcodeproj文件: %s", xcodeproj))
		}
	}

	utility.LogPrintInfo("项目路径验证通过")
	return nil
}

// determineProjectFileParam 确定项目文件参数
func determineProjectFileParam(config *BuildConfig) (string, error) {
	utility.LogPrintInfo("确定项目文件参数...")
	projectPath := config.ProjectPath
	workspace := config.Workspace
	project := config.Project
	var projectFilepParam string

	// 如果指定了workspace且文件存在
	if workspace != "" {
		workspacePath := filepath.Join(projectPath, workspace)
		// 使用find命令验证workspace文件是否存在且为目录
		cmd := exec.Command("find", projectPath, "-maxdepth", "1", "-name", workspace, "-type", "d")
		output, err := cmd.Output()
		if err != nil {
			return "", fmt.Errorf("验证workspace文件时出错: %w", err)
		}

		outputStr := strings.TrimSpace(string(output))
		if outputStr != "" {
			utility.LogPrintInfo(fmt.Sprintf("使用指定的workspace: %s", workspacePath))
			projectFilepParam = fmt.Sprintf("-workspace %s", workspacePath)
		} else {
			return "", fmt.Errorf("指定的workspace文件不存在或不是有效的workspace: %s", workspace)
		}
	}

	// 如果指定了project且文件存在
	if project != "" {
		projectFilePath := filepath.Join(projectPath, project)
		// 使用find命令验证project文件是否存在且为目录
		cmd := exec.Command("find", projectPath, "-maxdepth", "1", "-name", project, "-type", "d")
		output, err := cmd.Output()
		if err != nil {
			return "", fmt.Errorf("验证project文件时出错: %w", err)
		}

		outputStr := strings.TrimSpace(string(output))
		if outputStr != "" {
			if projectFilepParam == "" {
				utility.LogPrintInfo(fmt.Sprintf("使用指定的project: %s", projectFilePath))
				projectFilepParam = fmt.Sprintf("-project %s", projectFilePath)
			}
		} else {
			return "", fmt.Errorf("指定的project文件不存在或不是有效的project: %s", project)
		}
	}

	// 已确定项目参数
	if projectFilepParam != "" {
		// 需要再判断config.Project是否有值，没有值时自动获取，后面需要用到.xcodeproj/project.pbxproj文件
		if config.Project == "" {
			_, err := autoDetectProjectFile(config, false)
			if err != nil {
				return "", err
			}
		}
		return projectFilepParam, nil
	} else {
		// 两个都为空，自动检测
		return autoDetectProjectFile(config, true)
	}
}

// autoDetectProjectFile 自动检测项目文件
func autoDetectProjectFile(config *BuildConfig, checkAll bool) (string, error) {

	projectPath := config.ProjectPath
	utility.LogPrintInfo("自动检测项目文件...")
	var workspaceFile, projectFile string

	// 使用find命令查找.xcodeproj文件
	cmd := exec.Command("find", projectPath, "-maxdepth", "1", "-name", "*.xcodeproj", "-type", "d")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("搜索.xcodeproj文件时出错: %w", err)
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr != "" {
		projects := strings.Split(outputStr, "\n")
		if len(projects) > 1 {
			return "", fmt.Errorf("找到多个projects文件: %s，无法确定使用哪个，请通过--project指定", strings.ReplaceAll(outputStr, "\n", ","))
		} else {
			projectValue := projects[0]
			if projectValue != "" {
				projectFile = projectValue

				projectName := filepath.Base(projectFile)
				config.Project = projectName
			}
		}
	}

	if checkAll {
		// 使用find命令查找.xcworkspace文件
		cmd = exec.Command("find", projectPath, "-maxdepth", "1", "-name", "*.xcworkspace", "-type", "d")
		output, err = cmd.Output()
		if err != nil {
			return "", fmt.Errorf("搜索.xcworkspace文件时出错: %w", err)
		}

		outputStr = strings.TrimSpace(string(output))
		if outputStr != "" {
			workspaces := strings.Split(outputStr, "\n")
			if len(workspaces) > 1 {
				return "", fmt.Errorf("找到多个workspace文件: %s，无法确定使用哪个，请通过--workspace指定", strings.ReplaceAll(outputStr, "\n", ","))
			} else {
				workspaceValue := workspaces[0]
				if workspaceValue != "" {
					workspaceFile = workspaceValue

					workspaceName := filepath.Base(workspaceFile)
					config.Workspace = workspaceName
				}
			}
		}

		// 优先使用workspace
		if workspaceFile != "" {
			utility.LogPrintInfo(fmt.Sprintf("使用workspace文件: %s", workspaceFile))
			return fmt.Sprintf("-workspace %s", workspaceFile), nil
		}

		// 如果没有workspace，使用project
		if projectFile != "" {
			utility.LogPrintInfo(fmt.Sprintf("使用project文件: %s", projectFile))
			return fmt.Sprintf("-project %s", projectFile), nil
		}

		return "", fmt.Errorf("未找到有效的workspace或project文件")
	} else {
		return "", nil
	}
}

// determineScheme 确定scheme，并检查是否为Unity项目
func determineScheme(projectFileParam string, config *BuildConfig) (string, error) {
	utility.LogPrintInfo("确定构建scheme...")

	// 执行xcodebuild -list命令
	cmdArgs := []string{"xcodebuild", "-list"}
	cmdArgs = append(cmdArgs, strings.Fields(projectFileParam)...)

	utility.LogPrintInfo(fmt.Sprintf("执行命令: %s", strings.Join(cmdArgs, " ")))

	cmd := exec.Command(cmdArgs[0], cmdArgs[1:]...)
	cmd.Dir = config.ProjectPath
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行xcodebuild -list失败: %w", err)
	}

	outputStr := string(output)
	utility.LogPrintInfo("xcodebuild -list输出:")
	utility.LogPrintInfo(outputStr)

	// 解析schemes
	schemes := parseSchemes(outputStr)
	if len(schemes) == 0 {
		return "", fmt.Errorf("未找到任何scheme")
	}

	utility.LogPrintInfo(fmt.Sprintf("找到%d个scheme: %v", len(schemes), schemes))

	// 判断是否为Unity项目
	isUnityProject := utility.IsContain(schemes, "UnityFramework")
	config.IsUnityProject = isUnityProject

	// 如果指定了scheme，直接使用
	if config.Scheme != "" {
		utility.LogPrintInfo(fmt.Sprintf("使用指定的scheme: %s", config.Scheme))
		return config.Scheme, nil
	}

	// 如果只有一个scheme，直接使用
	if len(schemes) == 1 {
		utility.LogPrintInfo(fmt.Sprintf("使用唯一的scheme: %s", schemes[0]))
		return schemes[0], nil
	}

	// 多个schemes时，尝试找到与.xcodeproj文件同名的scheme
	return findMatchingScheme(schemes, config)
}

// parseSchemes 解析xcodebuild -list输出中的schemes
func parseSchemes(output string) []string {
	lines := strings.Split(output, "\n")
	var schemes []string
	inSchemesSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.Contains(line, "Schemes:") {
			inSchemesSection = true
			continue
		}

		if inSchemesSection {
			// 如果遇到空行或其他section，结束schemes解析
			if line == "" || strings.Contains(line, ":") {
				break
			}

			// 添加scheme
			if line != "" {
				schemes = append(schemes, line)
			}
		}
	}

	return schemes
}

// findMatchingScheme 查找与项目名匹配的scheme
func findMatchingScheme(schemes []string, config *BuildConfig) (string, error) {
	// 使用find命令获取.xcodeproj文件名（去除后缀）
	projectPath := config.ProjectPath
	cmd := exec.Command("find", projectPath, "-maxdepth", "1", "-name", "*.xcodeproj", "-type", "d")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("搜索.xcodeproj文件时出错: %w", err)
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" {
		return "", fmt.Errorf("未找到.xcodeproj文件")
	}

	// 获取第一个.xcodeproj文件的项目名称
	projects := strings.Split(outputStr, "\n")
	var projectName string
	for _, project := range projects {
		if project != "" {
			baseName := filepath.Base(project)
			projectName = strings.TrimSuffix(baseName, ".xcodeproj")
			break
		}
	}

	if projectName == "" {
		return "", fmt.Errorf("未找到有效的.xcodeproj文件")
	}

	utility.LogPrintInfo(fmt.Sprintf("项目名称: %s", projectName))

	// 查找匹配的scheme
	for _, scheme := range schemes {
		if scheme == projectName {
			utility.LogPrintInfo(fmt.Sprintf("找到匹配的scheme: %s", scheme))
			return scheme, nil
		}
	}

	// 如果没找到匹配的，使用第一个scheme
	utility.LogPrintWarning(fmt.Sprintf("未找到与项目名称匹配的scheme，使用第一个scheme: %s", schemes[0]))
	return schemes[0], nil
}

// getProjectBuildSettings 获取项目构建设置
func getProjectBuildSettings(projectFileParam, scheme, configuration, projectPath string) (*ProjectSettings, error) {
	utility.LogPrintInfo("获取项目构建设置...")

	// 构建xcodebuild -showBuildSettings命令
	cmdArgs := []string{"xcodebuild"}
	cmdArgs = append(cmdArgs, strings.Fields(projectFileParam)...)
	cmdArgs = append(cmdArgs, "-scheme", scheme)
	cmdArgs = append(cmdArgs, "-configuration", configuration)
	cmdArgs = append(cmdArgs, "-showBuildSettings")

	utility.LogPrintInfo(fmt.Sprintf("执行命令: %s", strings.Join(cmdArgs, " ")))

	cmd := exec.Command(cmdArgs[0], cmdArgs[1:]...)
	cmd.Dir = projectPath
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行xcodebuild -showBuildSettings失败: %w", err)
	}

	outputStr := string(output)
	// 解析构建设置
	settings := &ProjectSettings{}
	settings.ProductBundleIdentifier = extractBuildSetting(outputStr, "PRODUCT_BUNDLE_IDENTIFIER")
	settings.ProvisioningProfileSpecifier = extractBuildSetting(outputStr, "PROVISIONING_PROFILE_SPECIFIER")
	settings.DevelopmentTeam = extractBuildSetting(outputStr, "DEVELOPMENT_TEAM")
	settings.CodeSignIdentity = extractBuildSetting(outputStr, "CODE_SIGN_IDENTITY")
	settings.TargetName = extractBuildSetting(outputStr, "TARGET_NAME")
	settings.ARCHS = extractBuildSetting(outputStr, "ARCHS")
	settings.OTHER_LDFLAGS = extractBuildSetting(outputStr, "OTHER_LDFLAGS")
	settings.CodeSignEntitlements = extractBuildSetting(outputStr, "CODE_SIGN_ENTITLEMENTS")
	settings.InfoPlistFile = extractBuildSetting(outputStr, "INFOPLIST_FILE")

	utility.LogPrintInfo("获取当前项目构建设置")
	utility.LogPrintInfo(fmt.Sprintf("PRODUCT_BUNDLE_IDENTIFIER: %s", settings.ProductBundleIdentifier))
	utility.LogPrintInfo(fmt.Sprintf("PROVISIONING_PROFILE_SPECIFIER: %s", settings.ProvisioningProfileSpecifier))
	utility.LogPrintInfo(fmt.Sprintf("DEVELOPMENT_TEAM: %s", settings.DevelopmentTeam))
	utility.LogPrintInfo(fmt.Sprintf("CODE_SIGN_IDENTITY: %s", settings.CodeSignIdentity))
	utility.LogPrintInfo(fmt.Sprintf("TARGET_NAME: %s", settings.TargetName))
	utility.LogPrintInfo(fmt.Sprintf("ARCHS: %s", settings.ARCHS))
	utility.LogPrintInfo(fmt.Sprintf("OTHER_LDFLAGS: %s", settings.OTHER_LDFLAGS))
	utility.LogPrintInfo(fmt.Sprintf("CODE_SIGN_ENTITLEMENTS: %s", settings.CodeSignEntitlements))
	utility.LogPrintInfo(fmt.Sprintf("INFOPLIST_FILE: %s", settings.InfoPlistFile))

	return settings, nil
}

// extractBuildSetting 从xcodebuild输出中提取指定的构建设置值
func extractBuildSetting(output, key string) string {
	// 使用正则表达式匹配构建设置
	pattern := fmt.Sprintf(`[ ]+%s[ ]+=\s*(.*)`, regexp.QuoteMeta(key))
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(output)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return ""
}

// 修改项目配置，确保PRODUCT_BUNDLE_IDENTIFIER、PROVISIONING_PROFILE_SPECIFIER、DEVELOPMENT_TEAM等信息设置正确
func validateAndUpdateBuildSettings(config *BuildConfig, projectSettings *ProjectSettings) (*ExportOptions, error) {
	utility.LogPrintInfo("校验bundle-id设置...")

	// 1. 确定最终的bundle-id
	finalBundleID := config.BundleID
	if finalBundleID == "" {
		finalBundleID = projectSettings.ProductBundleIdentifier
		utility.LogPrintInfo(fmt.Sprintf("使用项目中的bundle-id: %s", finalBundleID))
	} else {
		utility.LogPrintInfo(fmt.Sprintf("使用指定的bundle-id: %s", finalBundleID))
	}

	//2. 确定使用的描述文件名,优先使用外面设置ProvisioningProfile -> 外面设置的CertificateType -> 当前项目设置的描述文件标识
	var provisioningProfileName string
	var provisioningProfileInfo *ProvisioningProfile
	if config.ProvisioningProfile != "" {
		//校验外部设置ProvisioningProfile是否正确，通过名字或路径进行查找，获取provisionProfile完整信息
		if !strings.HasSuffix(config.ProvisioningProfile, ".mobileprovision") && !strings.HasSuffix(config.ProvisioningProfile, ".provisionprofile") {
			//判断设置
			profileInfo, _ := findProfiles(config.Platform, config.ProvisioningProfile)
			if profileInfo != nil && profileInfo.AppID == finalBundleID {
				provisioningProfileInfo = profileInfo
				utility.LogPrintInfo(fmt.Sprintf("根据设置的profile名字:%s，自动匹配到provision文件路径: %s", provisioningProfileName, provisioningProfileInfo.Filename))
				provisioningProfileName = profileInfo.Name
			}
		} else {
			//传入的是 provisioningProfile文件路径
			profileInfo, _ := newProvisioningProfile(config.ProvisioningProfile, utility.PlatformOS_IOS)
			if profileInfo != nil && profileInfo.AppID == finalBundleID {
				provisioningProfileInfo = profileInfo
				utility.LogPrintInfo(fmt.Sprintf("根据设置的profile路径:%s，自动匹配到provision名: %s", provisioningProfileName, provisioningProfileInfo.Name))
				provisioningProfileName = profileInfo.Name
			}
		}
		if provisioningProfileInfo == nil {
			//校验是否查找到mobileprovision profile
			utility.LogPrintWarning(fmt.Sprintf("参数中设置的provision:%s 异常，未匹配到正确描述文件，通过其他方式尝试获取", config.ProvisioningProfile))
		}

	}

	if provisioningProfileInfo == nil && config.CertificateType != "" {
		//如果都没设置，根据CertificateType自动查找provisionProfile
		profileInfo, _ := getProfileByBundleIDAndType(finalBundleID, ProfileType(config.CertificateType), config.Platform)
		if profileInfo != nil {
			provisioningProfileInfo = profileInfo
			provisioningProfileName = profileInfo.Name
			utility.LogPrintInfo(fmt.Sprintf("根据设置的CertificateType类型: %s,自动匹配到mobileprovision文件路径: %s", config.CertificateType, profileInfo.Filename))
		}
	}

	if provisioningProfileInfo == nil && projectSettings.ProvisioningProfileSpecifier != "" {
		profileInfo, _ := findProfiles(config.Platform, projectSettings.ProvisioningProfileSpecifier)
		if profileInfo != nil && profileInfo.AppID == finalBundleID {
			provisioningProfileInfo = profileInfo
			utility.LogPrintInfo(fmt.Sprintf("根据当前项目ProvisioningProfileSpecifier:%s，自动匹配到provision文件路径: %s", projectSettings.ProvisioningProfileSpecifier, provisioningProfileInfo.Filename))
			provisioningProfileName = profileInfo.Name
		}
	}

	if provisioningProfileName == "" {
		return nil, fmt.Errorf("获取provision_profile_specifier失败,请设置--provision 或 --cert-type参数")
	}

	//3. 确定使用的签名证书，通过provisioningProfile匹配
	// 显示所有匹配的证书
	var codeSignIdentity string
	results, err := GetAllMatchingCertificates(provisioningProfileInfo.Filename)
	if err != nil {
		return nil, fmt.Errorf("匹配证书失败: %v", err)
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("未找到任何证书")
	}
	var certificateResult *MatchCertificateResult
	for _, result := range results {
		if result.Found && certificateResult == nil {
			// 先记录第一个匹配到的有效值，后面如果有匹配和项目里设置的一样的，再替换
			certificateResult = &result
			codeSignIdentity = certificateResult.IdentityName
		}

		if result.Found && projectSettings.CodeSignIdentity != "" && result.IdentityName == projectSettings.CodeSignIdentity {
			//验证当前工程里设置的，如果和项目里设置的一样，表示项目里设置的正确，使用当前项目里设置里的
			certificateResult = &result
			codeSignIdentity = certificateResult.IdentityName
			break
		}
	}
	if codeSignIdentity == "" {
		return nil, fmt.Errorf("获取签名证书失败,未匹配到和provisioningProfile相匹配的证书")
	}

	// 修改project.pbxproj文件
	utility.LogPrintInfo("校验后项目构建设置")
	utility.LogPrintInfo(fmt.Sprintf("PRODUCT_BUNDLE_IDENTIFIER: %s", finalBundleID))
	utility.LogPrintInfo(fmt.Sprintf("PROVISIONING_PROFILE_SPECIFIER: %s", provisioningProfileName))
	utility.LogPrintInfo(fmt.Sprintf("DEVELOPMENT_TEAM: %s", provisioningProfileInfo.TeamID))
	utility.LogPrintInfo(fmt.Sprintf("CODE_SIGN_IDENTITY: %s", codeSignIdentity))

	pbxprojPath := filepath.Join(config.ProjectPath, config.Project)

	curentBuildSettings, err := xcode.GetXCodeBuildSettings(pbxprojPath, projectSettings.TargetName, config.Configuration, xcode.ProductTypeApplication)
	if err != nil {
		return nil, fmt.Errorf("获取项目BuildSettings配置失败: %w", err)
	}

	modifySettingValues := []xcode.BuildSettingKeyValue{
		{Key: "PRODUCT_BUNDLE_IDENTIFIER", Value: finalBundleID},
		{Key: "PROVISIONING_PROFILE_SPECIFIER", Value: provisioningProfileName},
		{Key: "DEVELOPMENT_TEAM", Value: provisioningProfileInfo.TeamID},
		{Key: "CODE_SIGN_IDENTITY", Value: codeSignIdentity},
		{Key: "CODE_SIGN_STYLE", Value: "Manual"},
		{Key: "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES", Value: "YES"},
	}
	if config.Platform == utility.PlatformOS_IOS {
		if curentBuildSettings["PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]", Value: finalBundleID})
		}
		if curentBuildSettings["PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]", Value: provisioningProfileName})
		}
		if curentBuildSettings["DEVELOPMENT_TEAM[sdk=iphoneos*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "DEVELOPMENT_TEAM[sdk=iphoneos*]", Value: provisioningProfileInfo.TeamID})
		}
		if curentBuildSettings["CODE_SIGN_IDENTITY[sdk=iphoneos*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "CODE_SIGN_IDENTITY[sdk=iphoneos*]", Value: codeSignIdentity})
		}

		modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "ARCHS", Value: "arm64"})
	} else if config.Platform == utility.PlatformOS_OSX {
		if curentBuildSettings["PRODUCT_BUNDLE_IDENTIFIER[sdk=macosx*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "PRODUCT_BUNDLE_IDENTIFIER[sdk=macosx*]", Value: finalBundleID})
		}
		if curentBuildSettings["PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]", Value: provisioningProfileName})
		}
		if curentBuildSettings["DEVELOPMENT_TEAM[sdk=macosx*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "DEVELOPMENT_TEAM[sdk=macosx*]", Value: provisioningProfileInfo.TeamID})
		}
		if curentBuildSettings["CODE_SIGN_IDENTITY[sdk=macosx*]"] != nil {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "CODE_SIGN_IDENTITY[sdk=macosx*]", Value: codeSignIdentity})
		}
	}

	// 处理otherLinkerFlags
	//判断OTHER_LDFLAGS是否有添加-ObjC
	// 创建临时map来使用通用函数
	tempBuildSettings := map[string]interface{}{
		"OTHER_LDFLAGS": projectSettings.OTHER_LDFLAGS,
	}
	otherLinkerFlag := checkBuildSettingValue(tempBuildSettings, "OTHER_LDFLAGS", []string{"-ObjC"})
	otherLinkerFlagValue := strings.TrimSpace(otherLinkerFlag)
	if otherLinkerFlagValue != "" {
		modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "OTHER_LDFLAGS", Value: otherLinkerFlagValue})
		utility.LogPrintInfo(fmt.Sprintf("OTHER_LDFLAGS: %s", otherLinkerFlagValue))
	}

	//处理LD_RUNPATH_SEARCH_PATHS
	new_runPath_Search_paths := checkBuildSettingValue(curentBuildSettings, "LD_RUNPATH_SEARCH_PATHS", []string{"$(inherited)", "@executable_path/Frameworks"})
	if new_runPath_Search_paths != "" {
		modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "LD_RUNPATH_SEARCH_PATHS", Value: new_runPath_Search_paths})
		utility.LogPrintInfo(fmt.Sprintf("LD_RUNPATH_SEARCH_PATHS: %s", new_runPath_Search_paths))
	}

	//处理OTHER_CFLAGS 和 OTHER_CPLUSPLUSFLAGS
	if config.IsUnityProject {
		inheritedValue := "$(inherited)"
		newCFlags := checkBuildSettingValue(curentBuildSettings, "OTHER_CFLAGS", []string{inheritedValue})
		newCPlusPlusFlags := checkBuildSettingValue(curentBuildSettings, "OTHER_CPLUSPLUSFLAGS", []string{inheritedValue})

		if newCFlags != "" {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "OTHER_CFLAGS", Value: newCFlags})
		}

		if newCPlusPlusFlags != "" {
			modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "OTHER_CPLUSPLUSFLAGS", Value: newCPlusPlusFlags})
		}
	}

	if err := xcode.UpdateAndSaveBuildSettings(pbxprojPath, projectSettings.TargetName, config.Configuration, modifySettingValues, xcode.ProductTypeApplication); err != nil {
		return nil, fmt.Errorf("修改项目打包配置失败: %w", err)
	}
	exportOptions := &ExportOptions{}
	exportOptions.ProductBundleIdentifier = finalBundleID
	exportOptions.ProvisioningProfileSpecifier = provisioningProfileName
	exportOptions.DevelopmentTeam = provisioningProfileInfo.TeamID
	exportOptions.CodeSignIdentity = codeSignIdentity
	exportOptions.ProfileType = provisioningProfileInfo.Type
	exportOptions.ARCHS = projectSettings.ARCHS
	exportOptions.ProvisioningProfilePath = provisioningProfileInfo.Filename
	return exportOptions, nil
}

// executeCleanWithConfig 使用配置执行构建
func executeCleanWithConfig(projectFileParam string, config *BuildConfig) error {

	// 构建xcodebuild clean命令
	cmdArgs := []string{"xcodebuild"}
	cmdArgs = append(cmdArgs, "clean")
	cmdArgs = append(cmdArgs, strings.Fields(projectFileParam)...)
	cmdArgs = append(cmdArgs, "-alltargets")
	utility.LogPrintInfo(fmt.Sprintf("执行Clean命令: %s", strings.Join(cmdArgs, " ")))
	// 执行clean命令
	cmd := exec.Command(cmdArgs[0], cmdArgs[1:]...)
	cmd.Dir = config.ProjectPath

	// 实时输出Clean日志
	cmd.Stdout = &logWriter{prefix: "[CLEAN] "}
	cmd.Stderr = &logWriter{prefix: "[CLEAN] "}
	cmd.Run()
	utility.LogPrintSuccess("Clean完成")

	// 执行build命令
	return nil
}

// executeBuildWithConfig 使用配置执行构建
func executeBuildWithConfig(projectFileParam, scheme string, config *BuildConfig, exportOptions *ExportOptions) (string, error) {
	utility.LogPrintInfo("开始执行构建...")
	exportPath := config.OutputPath
	utility.LogPrintInfo(fmt.Sprintf("目标文件输出路径: %s", exportPath))

	xcarchivePath := filepath.Join(exportPath, "archive.xcarchive")
	// 构建xcodebuild archive命令
	cmdArgs := []string{"xcodebuild"}
	cmdArgs = append(cmdArgs, "archive")
	cmdArgs = append(cmdArgs, strings.Fields(projectFileParam)...)
	cmdArgs = append(cmdArgs, "-scheme", scheme)
	cmdArgs = append(cmdArgs, "-configuration", config.Configuration)
	cmdArgs = append(cmdArgs, "-archivePath", xcarchivePath)
	cmdArgs = append(cmdArgs, "DWARF_DSYM_FOLDER_PATH="+exportPath)
	cmdArgs = append(cmdArgs, "-UseModernBuildSystem=YES")

	if config.Platform == utility.PlatformOS_IOS {
		cmdArgs = append(cmdArgs, "-sdk", "iphoneos")
		cmdArgs = append(cmdArgs, "-destination", "generic/platform=iOS")
	} else if config.Platform == utility.PlatformOS_OSX {
		cmdArgs = append(cmdArgs, "-sdk", "macosx")
		cmdArgs = append(cmdArgs, "-destination", "generic/platform=macOS")
		if !strings.Contains(config.XcodeBuildArgs, "VALID_ARCHS") {
			cmdArgs = append(cmdArgs, "VALID_ARCHS="+exportOptions.ARCHS)
		}
	}

	// 追加额外的xcodebuild参数
	if config.XcodeBuildArgs != "" {
		additionalArgs := strings.Fields(config.XcodeBuildArgs)
		cmdArgs = append(cmdArgs, additionalArgs...)
	}

	utility.LogPrintInfo(fmt.Sprintf("执行构建命令: %s", strings.Join(cmdArgs, " ")))
	// 执行archive命令
	cmd := exec.Command(cmdArgs[0], cmdArgs[1:]...)
	cmd.Dir = config.ProjectPath

	// 实时输出构建日志
	cmd.Stdout = &logWriter{prefix: "[BUILD] "}
	cmd.Stderr = &logWriter{prefix: "[BUILD] "}

	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("archive构建失败: %w", err)
	}

	utility.LogPrintSuccess("Archive构建完成")

	// 导出ipa
	return xcarchivePath, nil
}

// exportIPAWithConfig 使用配置导出ipa文件
func exportIPAWithConfig(config *BuildConfig, exportOptions *ExportOptions) error {
	utility.LogPrintInfo("开始导出ipa/app文件...")

	exportPath := config.OutputPath
	archivePath := filepath.Join(exportPath, "archive.xcarchive")

	// 创建ExportOptions.plist
	exportOptionsPath := filepath.Join(config.OutputPath, "ExportOptionsFile.plist")
	exportOptionsContent := make(map[string]interface{})
	exportOptionsContent["teamID"] = exportOptions.DevelopmentTeam
	exportOptionsContent["method"] = getMethodExport(exportOptions.ProfileType)
	exportOptionsContent["compileBitcode"] = false
	exportOptionsContent["uploadSymbols"] = false

	if config.Platform == utility.PlatformOS_OSX && exportOptions.ProfileType == ProfileTypeDis {
		// macOS .app dis包时需要出.pkg包
		exportOptionsContent["installerSigningCertificate"] = "3rd Party Mac Developer Installer"
	}

	// 创建provisioningProfiles字典
	provisioningProfiles := make(map[string]interface{})
	provisioningProfiles[exportOptions.ProductBundleIdentifier] = exportOptions.ProvisioningProfileSpecifier
	exportOptionsContent["provisioningProfiles"] = provisioningProfiles

	exportOptionsContentBytes, err := plist.MarshalIndent(exportOptionsContent, plist.XMLFormat, "\t")
	if err != nil {
		return fmt.Errorf("ExportOptionsFile.plist失败: %w", err)
	}
	if err := os.WriteFile(exportOptionsPath, exportOptionsContentBytes, 0644); err != nil {
		return fmt.Errorf("ExportOptionsFile.plist失败: %w", err)
	}

	// 构建导出命令
	cmdArgs := []string{
		"xcodebuild",
		"-exportArchive",
		"-archivePath", archivePath,
		"-exportPath", exportPath,
		"-exportOptionsPlist", exportOptionsPath,
	}

	utility.LogPrintInfo(fmt.Sprintf("执行导出命令: %s", strings.Join(cmdArgs, " ")))

	cmd := exec.Command(cmdArgs[0], cmdArgs[1:]...)
	cmd.Dir = config.ProjectPath

	// 实时输出导出日志
	cmd.Stdout = &logWriter{prefix: "[EXPORT] "}
	cmd.Stderr = &logWriter{prefix: "[EXPORT] "}

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("exportArchive失败: %w", err)
	}
	return nil
}

// logWriter 用于实时输出构建日志
type logWriter struct {
	prefix string
}

func (w *logWriter) Write(p []byte) (n int, err error) {
	lines := strings.Split(strings.TrimRight(string(p), "\n"), "\n")
	for _, line := range lines {
		if line != "" {
			utility.LogPrintInfo(w.prefix + line)
		}
	}
	return len(p), nil
}

func getMethodExport(profileType ProfileType) string {
	switch profileType {
	case ProfileTypeDev:
		return "debugging"
	case ProfileTypeAdhoc:
		return "release-testing"
	case ProfileTypeDis:
		return "app-store-connect"
	case ProfileTypeEnterprise:
		return "enterprise"
	case ProfileTypeDeveloperID:
		return "developer-id"
	default:
		return "development"
	}
}

// handleUnityFrameworkBuildSettings 处理Unity项目的UnityFramework Target的OTHER_LDFLAGS设置
func handleUnityFrameworkBuildSettings(config *BuildConfig) error {
	// 判断是否为Unity项目
	if !config.IsUnityProject {
		utility.LogPrintInfo("非Unity项目，跳过UnityFramework OTHER_LDFLAGS检查")
		return nil
	}

	utility.LogPrintInfo("检测到Unity项目，开始检查UnityFramework Target的OTHER_LDFLAGS设置...")

	// 获取UnityFramework Target的构建设置
	pbxprojPath := filepath.Join(config.ProjectPath, config.Project)
	curentBuildSettings, err := xcode.GetXCodeBuildSettings(pbxprojPath, "UnityFramework", config.Configuration, xcode.ProductTypeFramework)
	if err != nil {
		return fmt.Errorf("获取UnityFramework BuildSettings配置失败: %w", err)
	}

	modifySettingValues := []xcode.BuildSettingKeyValue{}

	// 检查OTHER_LDFLAGS是否包含-ObjC
	new_other_ldflags := checkBuildSettingValue(curentBuildSettings, "OTHER_LDFLAGS", []string{"-ObjC"})
	if new_other_ldflags != "" {
		modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "OTHER_LDFLAGS", Value: new_other_ldflags})
	}

	//处理LD_RUNPATH_SEARCH_PATHS
	new_runPath_Search_paths := checkBuildSettingValue(curentBuildSettings, "LD_RUNPATH_SEARCH_PATHS", []string{"$(inherited)", "@executable_path/Frameworks"})
	if new_runPath_Search_paths != "" {
		modifySettingValues = append(modifySettingValues, xcode.BuildSettingKeyValue{Key: "LD_RUNPATH_SEARCH_PATHS", Value: new_runPath_Search_paths})
	}

	if len(modifySettingValues) > 0 {
		if err := xcode.UpdateAndSaveBuildSettings(pbxprojPath, "UnityFramework", config.Configuration, modifySettingValues, xcode.ProductTypeFramework); err != nil {
			return fmt.Errorf("更新UnityFramework OTHER_LDFLAGS设置失败: %w", err)
		}
	}

	utility.LogPrintSuccess("处理UnityFramework Target BuildSettings完成")
	return nil
}

func handleBuildAppDevConfig(buildConfig *BuildConfig, resignConfig *ResignConfig, projectSettings *ProjectSettings) error {
	if buildConfig.AppDevConfig == "" {
		utility.LogPrintInfo("AppDevConfig为空，跳过dev后台配置文件获取")
		return nil
	}

	// 通过判断AppDevConfig是否为空，来决定是否需要从dev后台请求初始化参数
	if downloadConfigPath, err := handleRequestAppDevConfig(resignConfig); err != nil {
		return fmt.Errorf("处理从Dev后台下载配置失败: %w", err)
	} else {
		if downloadConfigPath != "" {
			// 处理从后台拉取的配置文件，主要会添加配置文件、更新Info.plist和entitlement
			if err := handleDownloadDevConfig(resignConfig, downloadConfigPath, ""); err != nil {
				return fmt.Errorf("处理AppDevConfig失败: %w", err)
			}
		}
	}

	// 更新Info.plist
	plistPath := filepath.Join(buildConfig.ProjectPath, projectSettings.InfoPlistFile)
	if filepath.IsAbs(projectSettings.InfoPlistFile) {
		plistPath = projectSettings.InfoPlistFile
	}

	// 处理Info.plist文件的修改和合并
	if err := processPlistModifications(plistPath, resignConfig); err != nil {
		return fmt.Errorf("处理Info.plist修改失败: %w", err)
	}

	// 合并entitlements
	utility.LogPrintInfo("处理Entitlements")
	if projectSettings.CodeSignEntitlements != "" {
		// 项目已经设置了CODE_SIGN_ENTITLEMENTS
		entitlementsPath := filepath.Join(buildConfig.ProjectPath, projectSettings.CodeSignEntitlements)
		if filepath.IsAbs(projectSettings.CodeSignEntitlements) {
			entitlementsPath = projectSettings.CodeSignEntitlements
		}

		// 查看是否有从dev后台获取的配置，有则和entitlementsPath进行合并
		if _, error := mergeEntitlements(entitlementsPath, resignConfig.DevEntitlementInfo); error != nil {
			return error
		}
		return nil
	} else {
		// 项目未设置CODE_SIGN_ENTITLEMENTS
		// 将合并后的allEntitlements重新写入到provisionPath
		plistData, err := plist.MarshalIndent(resignConfig.DevEntitlementInfo, plist.XMLFormat, "\t")
		if err != nil {
			return fmt.Errorf("序列化entitlement数据失败: %w", err)
		}

		entitlementsName := buildConfig.Scheme + ".entitlements"
		entitlementsPath := filepath.Join(buildConfig.ProjectPath, entitlementsName)
		if err := os.WriteFile(entitlementsPath, plistData, 0644); err != nil {
			return fmt.Errorf("写入entitlements文件失败: %w", err)
		}
		utility.LogPrintInfo(fmt.Sprintf("生成entitlements文件: %s", entitlementsPath))

		// 更新到project.pbxproj工程文件
		pbxprojPath := filepath.Join(buildConfig.ProjectPath, buildConfig.Project)
		modifySettingValues := []xcode.BuildSettingKeyValue{
			{Key: "CODE_SIGN_ENTITLEMENTS", Value: entitlementsName},
		}
		if err := xcode.UpdateAndSaveBuildSettings(pbxprojPath, projectSettings.TargetName, buildConfig.Configuration, modifySettingValues, xcode.ProductTypeApplication); err != nil {
			return fmt.Errorf("修改CODE_SIGN_ENTITLEMENTS打包配置失败: %w", err)
		}
	}

	return nil
}

// 处理从dev后台下载的配置文件，添加到.app内
func handleBuildAdditionalFile(config *BuildConfig, xcarchivePath string, resignConfig *ResignConfig) error {
	// 获取.app应用名称 和 .app文件路径
	// 注意：appContentRootPath: iOS返回.app文件路径，macOS返回.app/Contents路径
	originAppUnzipPath := filepath.Join(xcarchivePath, "Products", "Applications")
	appName, appContentRootPath, err := getAppFileNameAndContentRootPath(originAppUnzipPath, config.Platform)
	if err != nil {
		return fmt.Errorf("获取可执行文件名称失败: %w", err)
	}
	resignConfig.AppName = appName
	utility.LogPrintInfo(fmt.Sprintf("可执行文件名称: %s", resignConfig.AppName))

	// 添加指定文件夹中的文件到.app目录
	if err := addFilesToApp(appContentRootPath, resignConfig.AddFilesPath); err != nil {
		return fmt.Errorf("添加文件失败: %w", err)
	}
	return nil
}

// checkBuildSettingValue 通用的构建设置值更新函数
// 检查currentBuildSettings中的targetKey是否包含targetValues中的每一项，如果不包含则添加
// 参数:
//   - currentBuildSettings: 当前构建设置map
//   - targetKey: 目标键名 (如 "OTHER_LDFLAGS")
//   - targetValues: 目标值数组 (如 []string{"-ObjC", "-lc++"})
//
// 返回: 更新后的字符串值，如果不需要更新则返回空字符串
func checkBuildSettingValue(currentBuildSettings map[string]interface{}, targetKey string, targetValues []string) string {
	currentValue := currentBuildSettings[targetKey]
	var existingValues []string
	needsUpdate := false

	// 解析当前值
	if currentValue == nil {
		// 如果当前值为空，所有目标值都需要添加
		existingValues = []string{}
		needsUpdate = true
	} else if valueStr, ok := currentValue.(string); ok {
		// 如果是字符串类型，按空格分割
		if strings.TrimSpace(valueStr) == "" {
			existingValues = []string{}
		} else {
			existingValues = strings.Fields(valueStr)
		}
	} else if valueInterface, ok := currentValue.([]interface{}); ok {
		// 如果是数组类型，转换为字符串数组
		existingValues = make([]string, len(valueInterface))
		for i, v := range valueInterface {
			existingValues[i] = fmt.Sprint(v)
		}
	} else {
		// 其他类型，转换为字符串后按空格分割
		valueStr := fmt.Sprint(currentValue)
		if strings.TrimSpace(valueStr) == "" {
			existingValues = []string{}
		} else {
			existingValues = strings.Fields(valueStr)
		}
	}

	// 检查每个目标值是否已存在，如果不存在则添加
	for _, targetValue := range targetValues {
		if !utility.IsContain(existingValues, targetValue) {
			existingValues = append(existingValues, targetValue)
			needsUpdate = true
		}
	}

	// 如果需要更新，返回新的值
	if needsUpdate {
		return strings.Join(existingValues, " ")
	}

	return ""
}
