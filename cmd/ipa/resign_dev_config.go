package ipa

import (
	"encoding/json"
	"fmt"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"howett.net/plist"
)

// copyDirWithIgnore 按原有层级拷贝目录，自动忽略隐藏文件，并支持自定义忽略文件列表
func copyDirWithIgnore(srcPath, dstPath string, ignoreFiles []string) error {
	return filepath.Walk(srcPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		fileName := filepath.Base(path)

		// 自动忽略隐藏文件
		if strings.HasPrefix(fileName, ".") {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 检查是否在自定义忽略列表中
		for _, ignoreFile := range ignoreFiles {
			if fileName == ignoreFile {
				utility.LogPrintInfo(fmt.Sprintf("跳过忽略文件: %s", fileName))
				if info.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}
		}

		// 计算相对路径
		relPath, err := filepath.Rel(srcPath, path)
		if err != nil {
			return err
		}

		targetPath := filepath.Join(dstPath, relPath)

		if info.IsDir() {
			// 创建目录
			return os.MkdirAll(targetPath, info.Mode())
		} else {
			// 拷贝文件
			_, err := utility.FileCopy(path, targetPath)
			if err != nil {
				return err
			}
			utility.LogPrintInfo(fmt.Sprintf("拷贝文件: %s -> %s", path, targetPath))
			return nil
		}
	})
}

// 处理从dev后台拉取的配置文件，替换到ipa包里
func handleDownloadDevConfig(config *ResignConfig, devNativeSDKPath string, unzipAppPath string) error {
	utility.LogPrintInfo(fmt.Sprintf("开始处理dev配置文件: %s", devNativeSDKPath))
	// 一、拷贝替换Dev后台下载的所有配置文件
	error := copyDevConfigFils(config, devNativeSDKPath)
	if error != nil {
		return error
	}

	// 二、根据dev后台配置的urlScheme等生成plist文件
	error = generatePlistBaseDevConfig(config, devNativeSDKPath)
	if error != nil {
		return error
	}

	// 三、根据dev后台配置的capabilities生成entitlement(推送、apple登录等)信息
	error = generateEntitlementBaseDevConfig(config, devNativeSDKPath, unzipAppPath)
	if error != nil {
		return error
	}
	return nil
}

// 一、拷贝替换Dev后台下载的所有配置文件
func copyDevConfigFils(config *ResignConfig, devNativeSDKPath string) error {
	// 1. 移除$devNativeSDKPath路径中最后一个层级，保存在$devConfigPath，在$devConfigPath目录下新建replacedConfig目录
	devConfigPath := filepath.Dir(devNativeSDKPath)
	replacedConfigPath := filepath.Join(devConfigPath, "replacedConfig")

	utility.LogPrintInfo(fmt.Sprintf("配置目录路径: %s", devConfigPath))
	utility.LogPrintInfo(fmt.Sprintf("替换配置目录路径: %s", replacedConfigPath))

	// 创建replacedConfig目录
	if err := os.MkdirAll(replacedConfigPath, 0755); err != nil {
		return fmt.Errorf("创建replacedConfig目录失败: %w", err)
	}
	utility.LogPrintSuccess(fmt.Sprintf("成功创建目录: %s", replacedConfigPath))

	// 2. 检查ResignConfig.AddFilesPath是否为空，不为空时，将ResignConfig.AddFilesPath下的所有文件和目录，按原有层级拷贝到$devConfigPath/replacedConfig
	if config.AddFilesPath != "" && utility.IsExist(config.AddFilesPath) {
		utility.LogPrintInfo(fmt.Sprintf("拷贝原有AddFilesPath内容: %s", config.AddFilesPath))

		if err := copyDirWithIgnore(config.AddFilesPath, replacedConfigPath, []string{}); err != nil {
			return fmt.Errorf("拷贝AddFilesPath内容失败: %w", err)
		}
		utility.LogPrintSuccess("成功拷贝原有AddFilesPath内容")
	}

	// 3. 将$devNativeSDKPath/Config目录下，除dynamic.json文件外的所有文件和目录，按原有层级全部拷贝到$devConfigPath/replacedConfig目录下
	configSourcePath := filepath.Join(devNativeSDKPath, "Config")
	if utility.IsExist(configSourcePath) {
		utility.LogPrintInfo(fmt.Sprintf("拷贝Config目录内容: %s", configSourcePath))

		// 忽略dynamic.json文件
		ignoreFiles := []string{"dynamic.json"}

		// macOS需要将资源文件添加到Resources目录下
		replacedResourcesPath := replacedConfigPath
		if config.Platform == utility.PlatformOS_OSX {
			replacedResourcesPath = filepath.Join(replacedResourcesPath, "Resources")
			// 创建replacedConfig目录
			if err := os.MkdirAll(replacedResourcesPath, 0755); err != nil {
				return fmt.Errorf("创建replacedConfig/Resources目录失败: %w", err)
			}
		}
		if err := copyDirWithIgnore(configSourcePath, replacedResourcesPath, ignoreFiles); err != nil {
			return fmt.Errorf("拷贝Config目录内容失败: %w", err)
		}
		utility.LogPrintSuccess("成功拷贝Config目录内容")
	} else {
		utility.LogPrintWarning(fmt.Sprintf("Config目录不存在: %s", configSourcePath))
	}

	// 4. 将$devConfigPath/replacedConfig的路径赋值给ResignConfig.AddFilesPath
	config.AddFilesPath = replacedConfigPath
	utility.LogPrintSuccess(fmt.Sprintf("已更新AddFilesPath为: %s", config.AddFilesPath))

	return nil
}

// 二、根据dev后台配置的urlScheme等生成plist文件
func generatePlistBaseDevConfig(config *ResignConfig, devNativeSDKPath string) error {
	utility.LogPrintInfo("开始根据dev配置生成plist文件")

	devConfigPath := filepath.Dir(devNativeSDKPath)
	// 1. 解析$devNativeSDKPath/Config/dynamic.json文件，并新建一个var devModifyPlistData map[string]interface{}
	dynamicJsonPath := filepath.Join(devNativeSDKPath, "Config", "dynamic.json")
	devModifyPlistData := make(map[string]interface{})

	if !utility.IsExist(dynamicJsonPath) {
		utility.LogPrintWarning(fmt.Sprintf("dynamic.json文件不存在: %s", dynamicJsonPath))
		return fmt.Errorf("dynamic.json文件不存在: %s", dynamicJsonPath)
	}

	// 读取并解析dynamic.json文件
	dynamicJsonData, err := os.ReadFile(dynamicJsonPath)
	if err != nil {
		return fmt.Errorf("读取dynamic.json文件失败: %w", err)
	}

	var dynamicConfig map[string]interface{}
	if err := json.Unmarshal(dynamicJsonData, &dynamicConfig); err != nil {
		return fmt.Errorf("解析dynamic.json文件失败: %w", err)
	}
	utility.LogPrintSuccess("成功解析dynamic.json文件")

	// 2. 判断ResignConfig.AdditionalPlistPath是否存在，存在时读取ResignConfig.AdditionalPlistPath，并保存到devModifyPlistData
	var cfBundleURLTypes []interface{}
	if config.AdditionalPlistPath != "" && utility.IsExist(config.AdditionalPlistPath) {
		utility.LogPrintInfo(fmt.Sprintf("读取现有的AdditionalPlistPath: %s", config.AdditionalPlistPath))

		additionalPlistData, err := utility.ParsePlist(config.AdditionalPlistPath)
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("读取AdditionalPlistPath失败: %v", err))
		} else {
			// 将现有的plist数据复制到devModifyPlistData
			for key, value := range additionalPlistData {
				devModifyPlistData[key] = value

				// 为了合并CFBundleURLTypes数组中的值
				if key == "CFBundleURLTypes" {
					if urlTypes, ok := value.([]interface{}); ok {
						cfBundleURLTypes = urlTypes
					}
				}
			}
			utility.LogPrintSuccess("成功读取现有的AdditionalPlistPath内容")
		}
	}

	// 3. 获取dynamic.json解析后的plist字段，将该字段内的所有key、value保存到devModifyPlistData
	if plistField, exists := dynamicConfig["plist"]; exists {
		utility.LogPrintInfo("处理dynamic.json中的plist字段")

		if plistMap, ok := plistField.(map[string]interface{}); ok {
			for key, value := range plistMap {
				devModifyPlistData[key] = value
				utility.LogPrintInfo(fmt.Sprintf("添加plist字段: %s", key))
			}
			utility.LogPrintSuccess("成功处理plist字段")
		} else {
			utility.LogPrintWarning("dynamic.json中的plist字段格式不正确")
		}
	}

	// 4. 获取dynamic.json解析后的urlTypes字段，转换为CFBundleURLTypes格式
	if urlTypesField, exists := dynamicConfig["urlTypes"]; exists {
		utility.LogPrintInfo("处理dynamic.json中的urlTypes字段")

		if urlTypesMap, ok := urlTypesField.(map[string]interface{}); ok {
			for urlName, urlScheme := range urlTypesMap {
				if schemeStr, ok := urlScheme.(string); ok {
					urlTypeEntry := map[string]interface{}{
						"CFBundleTypeRole":   "Editor",
						"CFBundleURLName":    urlName,
						"CFBundleURLSchemes": []interface{}{schemeStr},
					}
					cfBundleURLTypes = append(cfBundleURLTypes, urlTypeEntry)
					utility.LogPrintInfo(fmt.Sprintf("添加URL类型: %s -> %s", urlName, schemeStr))
				}
			}

			if len(cfBundleURLTypes) > 0 {
				devModifyPlistData["CFBundleURLTypes"] = cfBundleURLTypes
				utility.LogPrintSuccess(fmt.Sprintf("成功处理urlTypes字段，共添加%d个URL类型", len(cfBundleURLTypes)))
			}
		} else {
			utility.LogPrintWarning("dynamic.json中的urlTypes字段格式不正确")
		}
	}

	// 5. 获取dynamic.json解析后的capabilities字段，处理audio和remoteNotification权限
	if capabilitiesField, exists := dynamicConfig["capabilities"]; exists {
		utility.LogPrintInfo("处理dynamic.json中的capabilities字段")

		if capabilitiesMap, ok := capabilitiesField.(map[string]interface{}); ok {
			// 生成一个var devBackgroundModes map[string]interface{}
			var devBackgroundModes []interface{}

			// 判断devModifyPlistData中是否包含UIBackgroundModes字段
			if existingModes, hasBackgroundModes := devModifyPlistData["UIBackgroundModes"]; hasBackgroundModes {
				if modesArray, ok := existingModes.([]interface{}); ok {
					devBackgroundModes = modesArray
					utility.LogPrintInfo("读取现有的UIBackgroundModes字段")
				}
			}

			// 判断dynamic.json里capabilities是否有audio字段
			if audioValue, hasAudio := capabilitiesMap["audio"]; hasAudio {
				if audioBool, ok := audioValue.(bool); ok && audioBool {
					// 检查devBackgroundModes里是否已经有audio
					hasAudioMode := false
					for _, mode := range devBackgroundModes {
						if modeStr, ok := mode.(string); ok && modeStr == "audio" {
							hasAudioMode = true
							break
						}
					}
					// 如果没有audio，则添加
					if !hasAudioMode {
						devBackgroundModes = append(devBackgroundModes, "audio")
						utility.LogPrintInfo("添加audio后台模式")
					} else {
						utility.LogPrintInfo("audio后台模式已存在，跳过添加")
					}
				}
			}

			// 判断dynamic.json里capabilities是否有remoteNotification字段
			if remoteNotificationValue, hasRemoteNotification := capabilitiesMap["remoteNotification"]; hasRemoteNotification {
				if remoteNotificationBool, ok := remoteNotificationValue.(bool); ok && remoteNotificationBool {
					// 检查devBackgroundModes里是否已经有remote-notification
					hasRemoteNotificationMode := false
					for _, mode := range devBackgroundModes {
						if modeStr, ok := mode.(string); ok && modeStr == "remote-notification" {
							hasRemoteNotificationMode = true
							break
						}
					}
					// 如果没有remote-notification，则添加
					if !hasRemoteNotificationMode {
						devBackgroundModes = append(devBackgroundModes, "remote-notification")
						utility.LogPrintInfo("添加remote-notification后台模式")
					} else {
						utility.LogPrintInfo("remote-notification后台模式已存在，跳过添加")
					}
				}
			}

			// 如果有后台模式需要设置，则更新devModifyPlistData
			if len(devBackgroundModes) > 0 {
				devModifyPlistData["UIBackgroundModes"] = devBackgroundModes
				utility.LogPrintSuccess(fmt.Sprintf("成功处理capabilities字段，共设置%d个后台模式", len(devBackgroundModes)))
			}
		} else {
			utility.LogPrintWarning("dynamic.json中的capabilities字段格式不正确")
		}
	}

	// 6. 将devModifyPlistData的值通过plist.MarshalIndent方法，保存在一个plist文件中
	replacedInfoPlistPath := filepath.Join(devConfigPath, "replacedInfo.plist")

	if len(devModifyPlistData) > 0 {
		plistData, err := plist.MarshalIndent(devModifyPlistData, plist.XMLFormat, "\t")
		if err != nil {
			return fmt.Errorf("序列化plist数据失败: %w", err)
		}

		if err := os.WriteFile(replacedInfoPlistPath, plistData, 0644); err != nil {
			return fmt.Errorf("写入replacedInfo.plist文件失败: %w", err)
		}
		utility.LogPrintSuccess(fmt.Sprintf("成功生成replacedInfo.plist文件: %s", replacedInfoPlistPath))
	} else {
		utility.LogPrintWarning("没有需要修改的plist数据，跳过生成replacedInfo.plist文件")
		return nil
	}

	// 7. 将$devConfigPath/replacedInfo.plist保存到ResignConfig.AdditionalPlistPath
	if config.AdditionalPlistPath != "" {
		utility.LogPrintInfo(fmt.Sprintf("AdditionalPlistPath被替换: %s -> %s", config.AdditionalPlistPath, replacedInfoPlistPath))
	}
	config.AdditionalPlistPath = replacedInfoPlistPath
	utility.LogPrintSuccess(fmt.Sprintf("已更新AdditionalPlistPath为: %s", config.AdditionalPlistPath))

	return nil
}

// 三、根据dev后台配置的capabilities处理Entitlement
func generateEntitlementBaseDevConfig(config *ResignConfig, devNativeSDKPath string, unzipAppPath string) error {
	utility.LogPrintInfo("开始根据dev配置生成Entitlement")

	// 1. 获取dynamic.json解析后的capabilities字段，处理signInWithApple、pushNotifications 和 associatedDomains权限，生成一个变量var devCapabilities map[string]interface{}
	dynamicJsonPath := filepath.Join(devNativeSDKPath, "Config", "dynamic.json")
	devCapabilities := make(map[string]interface{})

	if !utility.IsExist(dynamicJsonPath) {
		utility.LogPrintWarning(fmt.Sprintf("dynamic.json文件不存在: %s", dynamicJsonPath))
		return fmt.Errorf("dynamic.json文件不存在: %s", dynamicJsonPath)
	}

	// 读取并解析dynamic.json文件
	dynamicJsonData, err := os.ReadFile(dynamicJsonPath)
	if err != nil {
		return fmt.Errorf("读取dynamic.json文件失败: %w", err)
	}

	var dynamicConfig map[string]interface{}
	if err := json.Unmarshal(dynamicJsonData, &dynamicConfig); err != nil {
		return fmt.Errorf("解析dynamic.json文件失败: %w", err)
	}

	// 获取capabilities字段
	capabilities, exists := dynamicConfig["capabilities"]
	if !exists {
		utility.LogPrintWarning("dynamic.json中未找到capabilities字段")
		return nil
	}

	capabilitiesMap, ok := capabilities.(map[string]interface{})
	if !ok {
		utility.LogPrintWarning("dynamic.json中的capabilities字段格式不正确")
		return nil
	}

	utility.LogPrintSuccess("成功解析dynamic.json中的capabilities字段")
	// 2. 处理signInWithApple,判断capabilities中是否包含signInWithApple字段，并且值为true，如果条件成立，则向devCapabilities添加一个com.apple.developer.applesignin一项，内容固定为:["Default"]
	if signInWithApple, hasSignInWithApple := capabilitiesMap["signInWithApple"]; hasSignInWithApple {
		if signInWithAppleBool, ok := signInWithApple.(bool); ok && signInWithAppleBool {
			devCapabilities["com.apple.developer.applesignin"] = []interface{}{"Default"}
			utility.LogPrintInfo("添加com.apple.developer.applesignin权限")
		}
	}

	// 3. 处理associatedDomains,判断capabilities中是否包含associatedDomains字段，associatedDomains字段是个字符串数组，格式类似["applinks:safestatic.games.laohu.com"，"applinks:safestatic.games.qq.com"]，获取associatedDomains的值，如果不为空，则向devCapabilities添加一个com.apple.developer.associated-domains一项，内容为获取到的associatedDomains的数组值
	if associatedDomains, hasAssociatedDomains := capabilitiesMap["associatedDomains"]; hasAssociatedDomains {
		if associatedDomainsArray, ok := associatedDomains.([]interface{}); ok && len(associatedDomainsArray) > 0 {
			devCapabilities["com.apple.developer.associated-domains"] = associatedDomainsArray
			utility.LogPrintInfo(fmt.Sprintf("添加com.apple.developer.associated-domains权限，共%d个域名", len(associatedDomainsArray)))
		}
	}

	// 4. 处理pushNotifications,判断capabilities中是否包含pushNotifications字段，并且值为true，如果条件成立，则向devCapabilities添加一个aps-environment一项，内容是个字符串pushValue变量
	if pushNotifications, hasPushNotifications := capabilitiesMap["pushNotifications"]; hasPushNotifications {
		if pushNotificationsBool, ok := pushNotifications.(bool); ok && pushNotificationsBool {
			utility.LogPrintInfo("处理pushNotifications权限")

			// 获取ProvisioningProfile路径
			var provisioningProfilePath string
			if config.ProvisioningProfile == "" {
				// 如果config.ProvisioningProfile为空，则调用resign.go中的getEmbeddedProvisioningProfile方法
				provisioningProfilePath = getEmbeddedProvisioningProfile(unzipAppPath, config.Platform)
			} else {
				provisioningProfilePath = config.ProvisioningProfile
			}

			if provisioningProfilePath != "" {
				// 通过命令行执行 security cms -D -i ${ProvisioningProfilePath} | grep -i "aps-environment" -A 1的命令，获取到pushValue的值
				utility.LogPrintInfo(fmt.Sprintf("从ProvisioningProfile提取aps-environment: %s", provisioningProfilePath))

				cmd := exec.Command("bash", "-c", fmt.Sprintf("security cms -D -i \"%s\" | grep -i \"aps-environment\" -A 1", provisioningProfilePath))
				output, err := cmd.Output()
				if err != nil {
					utility.LogPrintWarning(fmt.Sprintf("执行security cms命令失败: %v", err))
				} else {
					outputStr := string(output)
					if strings.Contains(outputStr, "aps-environment") {
						// 提取pushValue的值，但此时pushValue的值为<string>development</string>，需要再移除<string>标签
						lines := strings.Split(outputStr, "\n")
						for _, line := range lines {
							line = strings.TrimSpace(line)
							if strings.HasPrefix(line, "<string>") && strings.HasSuffix(line, "</string>") {
								pushValue := strings.TrimPrefix(line, "<string>")
								pushValue = strings.TrimSuffix(pushValue, "</string>")
								if pushValue != "" {
									devCapabilities["aps-environment"] = pushValue
									utility.LogPrintInfo(fmt.Sprintf("添加aps-environment权限: %s", pushValue))
									break
								}
							}
						}
					} else {
						// 如果通过上面的security cms -D命令，没获取到aps-environment的值，则打印一个警告，提示ProvisioningProfile中不支持aps-environment权限，此时可跳过pushNotifications的处理，不需要将aps-environment添加到devCapabilities
						utility.LogPrintWarning("ProvisioningProfile中不支持aps-environment权限，跳过pushNotifications处理")
					}
				}
			} else {
				utility.LogPrintWarning("未找到ProvisioningProfile文件，跳过pushNotifications处理")
			}
		}
	}

	// 5. 将devCapabilities的值赋值给config.DevEntitlementInfo
	config.DevEntitlementInfo = devCapabilities
	utility.LogPrintSuccess(fmt.Sprintf("成功生成DevEntitlementInfo，共包含%d个权限", len(devCapabilities)))

	// 6. [可去掉，为了测试时查看devCapabilities是否正确] 将devCapabilities通过plist.MarshalIndent方法保存在$devConfigPath下的replacedEntitlement.plist文件
	devConfigPath := filepath.Dir(devNativeSDKPath)
	if len(devCapabilities) > 0 {
		replacedEntitlementPath := filepath.Join(devConfigPath, "replacedEntitlement.plist")

		entitlementData, err := plist.MarshalIndent(devCapabilities, plist.XMLFormat, "\t")
		if err != nil {
			return fmt.Errorf("序列化Entitlement数据失败: %w", err)
		}

		if err := os.WriteFile(replacedEntitlementPath, entitlementData, 0644); err != nil {
			return fmt.Errorf("写入replacedEntitlement.plist文件失败: %w", err)
		}
		utility.LogPrintSuccess(fmt.Sprintf("成功生成replacedEntitlement.plist文件: %s", replacedEntitlementPath))
	} else {
		utility.LogPrintWarning("没有需要修改的Entitlement数据，跳过生成replacedEntitlement.plist文件")
	}

	return nil
}

// 合并Entitlements
func mergeEntitlements(provisionPath string, entitlementsInfo map[string]interface{}) (string, error) {
	// 读取provisionPath的文件内容到变量allEntitlements
	allEntitlements, err := utility.ParsePlist(provisionPath)
	if err != nil {
		return "", fmt.Errorf("读取plist文件失败: %w", err)
	}

	// 遍历entitlementsInfo的key
	for key, value := range entitlementsInfo {
		switch v := value.(type) {
		case string:
			// 当key对应的value值为string时，如果allEntitlements里没有则添加
			if _, exists := allEntitlements[key]; !exists {
				allEntitlements[key] = v
				utility.LogPrintInfo(fmt.Sprintf("添加字符串权限: %s = %s", key, v))
			} else {
				utility.LogPrintInfo(fmt.Sprintf("字符串权限已存在，忽略: %s", key))
			}

		case map[string]interface{}:
			// 当key对应的value为map时，检查allEntitlements中是否有，没有则添加，有则合并map的值
			if existingValue, exists := allEntitlements[key]; exists {
				if existingMap, ok := existingValue.(map[string]interface{}); ok {
					// 合并map的值
					for mapKey, mapValue := range v {
						if _, mapExists := existingMap[mapKey]; !mapExists {
							existingMap[mapKey] = mapValue
							utility.LogPrintInfo(fmt.Sprintf("合并map权限: %s.%s", key, mapKey))
						} else {
							utility.LogPrintInfo(fmt.Sprintf("map权限已存在，忽略: %s.%s", key, mapKey))
						}
					}
				} else {
					// 如果现有值不是map类型，直接替换
					allEntitlements[key] = v
					utility.LogPrintInfo(fmt.Sprintf("替换为map权限: %s", key))
				}
			} else {
				// 没有则添加
				allEntitlements[key] = v
				utility.LogPrintInfo(fmt.Sprintf("添加map权限: %s", key))
			}

		case []interface{}:
			// 当key对应的value为数组时，检查allEntitlements中是否有，没有则添加，有则遍历数组value，依次判断是否已包含
			if existingValue, exists := allEntitlements[key]; exists {
				if existingArray, ok := existingValue.([]interface{}); ok {
					// 遍历数组value，依次判断allEntitlements中该key对应value数组中是否已包含
					for _, arrayItem := range v {
						found := false
						for _, existingItem := range existingArray {
							if arrayItem == existingItem {
								found = true
								break
							}
						}
						// 不包含则追加
						if !found {
							existingArray = append(existingArray, arrayItem)
							utility.LogPrintInfo(fmt.Sprintf("追加数组权限项: %s[%v]", key, arrayItem))
						} else {
							utility.LogPrintInfo(fmt.Sprintf("数组权限项已存在，忽略: %s[%v]", key, arrayItem))
						}
					}
					allEntitlements[key] = existingArray
				} else {
					// 如果现有值不是数组类型，直接替换
					allEntitlements[key] = v
					utility.LogPrintInfo(fmt.Sprintf("替换为数组权限: %s", key))
				}
			} else {
				// 没有则添加
				allEntitlements[key] = v
				utility.LogPrintInfo(fmt.Sprintf("添加数组权限: %s", key))
			}

		default:
			// 其他类型直接添加或替换
			if _, exists := allEntitlements[key]; !exists {
				allEntitlements[key] = v
				utility.LogPrintInfo(fmt.Sprintf("添加其他类型权限: %s", key))
			} else {
				utility.LogPrintInfo(fmt.Sprintf("其他类型权限已存在，忽略: %s", key))
			}
		}
	}

	// 将合并后的allEntitlements重新写入到provisionPath
	plistData, err := plist.MarshalIndent(allEntitlements, plist.XMLFormat, "\t")
	if err != nil {
		return "", fmt.Errorf("序列化plist数据失败: %w", err)
	}

	if err := os.WriteFile(provisionPath, plistData, 0644); err != nil {
		return "", fmt.Errorf("写入plist文件失败: %w", err)
	}

	utility.LogPrintSuccess(fmt.Sprintf("成功合并并写入entitlements到: %s", provisionPath))
	return provisionPath, nil
}
