package ipa

import (
	"encoding/json"
	"fmt"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

// ProfileType 描述配置文件类型
type ProfileType string

const (
	ProfileTypeDev         ProfileType = "dev"   // 开发类型
	ProfileTypeAdhoc       ProfileType = "adhoc" // Ad Hoc类型
	ProfileTypeDis         ProfileType = "dis"   // 分发类型
	ProfileTypeEnterprise  ProfileType = "ent"   // iOS企业类型
	ProfileTypeDeveloperID ProfileType = "did"   // macOS 非AppStore发布(侧载)
)

func toProfileType(s string) (ProfileType, error) {
	switch s {
	case string(ProfileTypeDev):
		return ProfileTypeDev, nil
	case string(ProfileTypeAdhoc):
		return ProfileTypeAdhoc, nil
	case string(ProfileTypeDis):
		return ProfileTypeDis, nil
	case string(ProfileTypeEnterprise):
		return ProfileTypeEnterprise, nil
	case string(ProfileTypeDeveloperID):
		return ProfileTypeDeveloperID, nil
	default:
		return "", fmt.Errorf("invalid ProfileType: %s", s)
	}
}

// ProvisioningProfile 描述配置文件结构
type ProvisioningProfile struct {
	Filename       string //路径
	Name           string //名字
	Created        time.Time
	Expires        time.Time
	AppID          string //bundleId
	TeamID         string
	Entitlements   map[string]interface{}
	FullIdentifier string      // application identifier，方便去重
	Type           ProfileType // 配置文件类型
}

// GetProfiles 获取指定平台的配置文件列表，dedup是否去重
func getProfiles(platform utility.PlatformOS, dedup bool) ([]ProvisioningProfile, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("获取用户主目录失败: %w", err)
	}

	// macOS Sequoia 路径
	macOSSequoiaPath := filepath.Join(homeDir, "Library", "Developer", "Xcode", "UserData", "Provisioning Profiles")

	var allProfiles []ProvisioningProfile
	profilePaths := []string{macOSSequoiaPath}

	for _, profilesPath := range profilePaths {
		if !utility.IsExist(profilesPath) {
			continue
		}

		entries, err := os.ReadDir(profilesPath)
		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("读取配置文件目录失败: %s, 错误: %v", profilesPath, err))
			continue
		}

		// 根据平台查找
		var platformSuffix = ".mobileprovision"
		if platform == utility.PlatformOS_OSX {
			platformSuffix = ".provisionprofile"
		}
		for _, entry := range entries {
			if entry.IsDir() || !strings.HasSuffix(entry.Name(), platformSuffix) {
				continue
			}

			profilePath := filepath.Join(profilesPath, entry.Name())
			if profile, err := newProvisioningProfile(profilePath, platform); err == nil {
				if profile != nil {
					allProfiles = append(allProfiles, *profile)
				}
			} else {
				utility.LogPrintWarning(fmt.Sprintf("解析配置文件失败: %s, 错误: %v", entry.Name(), err))
			}
		}
	}

	// 按创建时间降序排序
	sort.Slice(allProfiles, func(i, j int) bool {
		return allProfiles[i].Created.After(allProfiles[j].Created)
	})

	if dedup {
		// 用 FullIdentifier + Type 组合去重，只有标识符和类型都相同才算重复
		seen := make(map[string]bool)
		var uniqueProfiles []ProvisioningProfile

		for _, profile := range allProfiles {
			// 使用 FullIdentifier + Type 作为去重键
			dedupeKey := fmt.Sprintf("%s_%s", profile.FullIdentifier, profile.Type)
			if !seen[dedupeKey] {
				seen[dedupeKey] = true
				uniqueProfiles = append(uniqueProfiles, profile)
			}
		}
		return uniqueProfiles, nil
	}
	return allProfiles, nil
}

// findProfiles 根据配置文件名称查找指定的ProvisioningProfile完整路径
func findProfiles(platform utility.PlatformOS, name string) (*ProvisioningProfile, error) {
	profiles, err := getProfiles(platform, false)
	if err != nil {
		return nil, fmt.Errorf("获取配置文件列表失败: %w", err)
	}

	// 查找匹配名称的配置文件
	for _, profile := range profiles {
		if profile.Name == name {
			return &profile, nil
		}
	}

	return nil, fmt.Errorf("未找到名称为 '%s' 的配置文件", name)
}

// NewProvisioningProfile 创建新的配置文件实例
func newProvisioningProfile(filename string, platform utility.PlatformOS) (*ProvisioningProfile, error) {
	// 使用security命令解析mobileprovision文件
	cmd := exec.Command("security", "cms", "-D", "-i", filename)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行security命令失败: %w", err)
	}

	// 查找XML开始位置
	outputStr := string(output)
	xmlIndex := strings.Index(outputStr, "<?xml")
	if xmlIndex == -1 {
		return nil, fmt.Errorf("未找到XML内容")
	}

	rawXML := outputStr[xmlIndex:]

	// 解析plist
	results, err := utility.ParsePlistBytes([]byte(rawXML))
	if err != nil {
		return nil, fmt.Errorf("解析plist失败: %w", err)
	}

	// 提取基本信息
	expirationDate, ok := results["ExpirationDate"].(time.Time)
	if !ok {
		return nil, fmt.Errorf("无法获取过期时间")
	}

	creationDate, ok := results["CreationDate"].(time.Time)
	if !ok {
		return nil, fmt.Errorf("无法获取创建时间")
	}

	name, ok := results["Name"].(string)
	if !ok {
		return nil, fmt.Errorf("无法获取配置文件名称")
	}

	// 根据平台查找
	var platformIdentifierKey = "application-identifier"
	if platform == utility.PlatformOS_OSX {
		platformIdentifierKey = "com.apple.application-identifier"
	}
	entitlements, ok := results["Entitlements"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无法获取entitlements")
	}

	// 根据平台选择identifier key
	var applicationIdentifier string
	if identifier, ok := entitlements[platformIdentifierKey].(string); ok {
		applicationIdentifier = identifier
	}

	// 分离TeamID和AppID
	periodIndex := strings.Index(applicationIdentifier, ".")
	if periodIndex == -1 {
		return nil, fmt.Errorf("应用标识符格式错误")
	}

	teamID := applicationIdentifier[:periodIndex]
	appID := applicationIdentifier[periodIndex+1:]
	profileType := determineProfileType(results, entitlements, platform)
	profile := &ProvisioningProfile{
		Filename:       filename,
		Name:           name,
		Created:        creationDate,
		Expires:        expirationDate,
		AppID:          appID,
		TeamID:         teamID,
		Entitlements:   entitlements,
		FullIdentifier: applicationIdentifier,
		Type:           profileType,
	}

	return profile, nil
}

// GetProfileByBundleID 根据Bundle ID查找匹配的配置文件
func getProfileByBundleID(bundleID string, platform utility.PlatformOS) (*ProvisioningProfile, error) {
	profiles, err := getProfiles(platform, true)
	if err != nil {
		return nil, err
	}

	// 首先查找完全匹配的
	for _, profile := range profiles {
		if profile.AppID == bundleID {
			return &profile, nil
		}
	}

	// 然后查找通配符匹配的
	for _, profile := range profiles {
		if strings.HasSuffix(profile.AppID, "*") {
			prefix := strings.TrimSuffix(profile.AppID, "*")
			if strings.HasPrefix(bundleID, prefix) {
				return &profile, nil
			}
		}
	}

	return nil, fmt.Errorf("未找到匹配Bundle ID '%s' 的配置文件", bundleID)
}

// determineProfileType 根据配置文件内容确定类型
func determineProfileType(plistData map[string]interface{}, entitlements map[string]interface{}, platform utility.PlatformOS) ProfileType {
	if platform == utility.PlatformOS_OSX {
		// 规则1: 检查文件内容里是否有ProvisionsAllDevices字段，有则为企业类型
		if _, hasProvisionsAllDevices := plistData["ProvisionsAllDevices"]; hasProvisionsAllDevices {
			return ProfileTypeDeveloperID
		}

		// 规则2: 检查文件内容里面是否有ProvisionedDevices字段，如果没有则为dis类型
		if _, hasProvisionedDevices := plistData["ProvisionedDevices"]; !hasProvisionedDevices {
			return ProfileTypeDis
		}

		// 规则3: 目前没生成过OSX adhoc描述文件，不能详细判断adhoc和dev，都按dev返回
		return ProfileTypeDev
	} else {
		// 规则1: 检查文件内容里是否有ProvisionsAllDevices字段，有则为企业类型
		if _, hasProvisionsAllDevices := plistData["ProvisionsAllDevices"]; hasProvisionsAllDevices {
			return ProfileTypeEnterprise
		}

		// 规则2: 检查文件内容里面是否有ProvisionedDevices字段，如果没有则为dis类型
		if _, hasProvisionedDevices := plistData["ProvisionedDevices"]; !hasProvisionedDevices {
			return ProfileTypeDis
		}

		// 规则3: 检查Entitlements下是否有get-task-allow字段，如果有，且为true，则为dev类型，没有则为adhoc
		if getTaskAllow, exists := entitlements["get-task-allow"]; exists {
			if allow, ok := getTaskAllow.(bool); ok && allow {
				return ProfileTypeDev
			}
		}

		// 如果有ProvisionedDevices但get-task-allow不为true，则为adhoc类型
		return ProfileTypeAdhoc
	}
}

// GetTypeString 获取配置文件类型的中文描述
func (p *ProvisioningProfile) GetTypeString() string {
	switch p.Type {
	case ProfileTypeDev:
		return "开发"
	case ProfileTypeAdhoc:
		return "AdHoc"
	case ProfileTypeDis:
		return "AppStore"
	case ProfileTypeEnterprise:
		return "企业"
	case ProfileTypeDeveloperID:
		return "DeveloperID"
	default:
		return "未知"
	}
}

// IsProfileExpired 检查配置文件是否已过期
func (p *ProvisioningProfile) IsProfileExpired() bool {
	return time.Now().After(p.Expires)
}

// GetProfilesByTeamID 根据Team ID获取配置文件列表
func getProfilesByTeamID(teamID string, platform utility.PlatformOS) ([]ProvisioningProfile, error) {
	profiles, err := getProfiles(platform, true)
	if err != nil {
		return nil, err
	}

	var matchedProfiles []ProvisioningProfile
	for _, profile := range profiles {
		if profile.TeamID == teamID {
			matchedProfiles = append(matchedProfiles, profile)
		}
	}

	return matchedProfiles, nil
}

// GetProfilesByType 根据配置文件类型获取配置文件列表
func GetProfilesByType(profileType ProfileType, platform utility.PlatformOS) ([]ProvisioningProfile, error) {
	profiles, err := getProfiles(platform, true)
	if err != nil {
		return nil, err
	}

	var matchedProfiles []ProvisioningProfile
	for _, profile := range profiles {
		if profile.Type == profileType {
			matchedProfiles = append(matchedProfiles, profile)
		}
	}

	return matchedProfiles, nil
}

// GetProfileByBundleIDAndType 根据Bundle ID和类型查找匹配的配置文件
func getProfileByBundleIDAndType(bundleID string, profileType ProfileType, platform utility.PlatformOS) (*ProvisioningProfile, error) {
	profiles, err := getProfiles(platform, true)
	if err != nil {
		return nil, err
	}

	// 首先查找完全匹配的指定类型配置文件
	for _, profile := range profiles {
		if profile.AppID == bundleID && profile.Type == profileType {
			return &profile, nil
		}
	}

	// 然后查找通配符匹配的指定类型配置文件
	for _, profile := range profiles {
		if strings.HasSuffix(profile.AppID, "*") && profile.Type == profileType {
			prefix := strings.TrimSuffix(profile.AppID, "*")
			if strings.HasPrefix(bundleID, prefix) {
				return &profile, nil
			}
		}
	}

	return nil, fmt.Errorf("未找到匹配Bundle ID '%s' 和类型 '%s' 的配置文件", bundleID, profileType)
}

// ProfileOutput 用于JSON输出的配置文件结构
type ProfileOutput struct {
	Name           string    `json:"name"`
	FullIdentifier string    `json:"fullIdentifier"`
	Type           string    `json:"type"`
	TypeCode       string    `json:"typeCode"`
	AppID          string    `json:"appId"`
	TeamID         string    `json:"teamId"`
	Created        time.Time `json:"created"`
	Expires        time.Time `json:"expires"`
	IsExpired      bool      `json:"isExpired"`
	Filename       string    `json:"filename"`
}

// ProfilesOutput 用于JSON输出的完整结构
type ProfilesOutput struct {
	Total    int             `json:"total"`
	Profiles []ProfileOutput `json:"profiles"`
}

// ListProvisioningProfiles 列出iOS、macOS配置文件，支持不同输出格式
func ListProvisioningProfiles(outputJSON bool, platform utility.PlatformOS) error {
	profiles, err := getProfiles(platform, true)
	if err != nil {
		return fmt.Errorf("获取配置文件失败: %w", err)
	}

	if len(profiles) == 0 {
		if outputJSON {
			emptyOutput := ProfilesOutput{
				Total:    0,
				Profiles: []ProfileOutput{},
			}
			jsonData, _ := json.MarshalIndent(emptyOutput, "", "  ")
			fmt.Println(string(jsonData))
		} else {
			utility.LogPrintWarning("未找到配置文件")
		}
		return nil
	}

	if outputJSON {
		return outputProfilesAsJSON(profiles)
	} else {
		return outputProfilesAsTable(profiles)
	}
}

// outputProfilesAsJSON 以JSON格式输出配置文件
func outputProfilesAsJSON(profiles []ProvisioningProfile) error {
	var profileOutputs []ProfileOutput

	for _, profile := range profiles {
		profileOutput := ProfileOutput{
			Name:           profile.Name,
			FullIdentifier: profile.FullIdentifier,
			Type:           profile.GetTypeString(),
			TypeCode:       string(profile.Type),
			AppID:          profile.AppID,
			TeamID:         profile.TeamID,
			Created:        profile.Created,
			Expires:        profile.Expires,
			IsExpired:      profile.IsProfileExpired(),
			Filename:       profile.Filename,
		}
		profileOutputs = append(profileOutputs, profileOutput)
	}

	output := ProfilesOutput{
		Total:    len(profiles),
		Profiles: profileOutputs,
	}

	jsonData, err := json.MarshalIndent(output, "", "  ")
	if err != nil {
		return fmt.Errorf("生成JSON输出失败: %w", err)
	}

	fmt.Println(string(jsonData))
	return nil
}

// outputProfilesAsTable 以表格格式输出配置文件
func outputProfilesAsTable(profiles []ProvisioningProfile) error {
	utility.LogPrintSuccess(fmt.Sprintf("找到 %d 个配置文件\n", len(profiles)))

	// 输出表头
	utility.LogPrintInfo("配置文件列表:")
	utility.LogPrintInfo("序号 | 配置文件名称 | 唯一标识 | 类型")
	utility.LogPrintInfo("-----|-------------|----------|------")

	// 输出每个配置文件的信息
	for i, profile := range profiles {
		utility.LogPrintInfo(fmt.Sprintf("%4d | %s | %s | %s",
			i+1,
			profile.Name,
			profile.FullIdentifier,
			profile.GetTypeString()))
	}

	return nil
}
