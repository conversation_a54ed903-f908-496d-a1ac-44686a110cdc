package ipa

import (
	"fmt"
	"io"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// 获取应用名称
// 注意：appContentRootPath: iOS返回.app文件路径，macOS返回.app/Contents路径
func getAppFileNameAndContentRootPath(tempDir string, platform utility.PlatformOS) (string, string, error) {
	var appName string
	var appContentRootPath string

	appFileDirPath := tempDir
	if platform == utility.PlatformOS_IOS {
		appFileDirPath = filepath.Join(tempDir, "Payload")
		if !utility.IsExist(appFileDirPath) {
			appFileDirPath = tempDir
		}
	}

	err := filepath.Walk(appFileDirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() && strings.HasSuffix(info.Name(), ".app") {
			appName = strings.TrimSuffix(info.Name(), ".app")
			if platform == utility.PlatformOS_OSX {
				appContentRootPath = filepath.Join(path, "Contents")
			} else {
				appContentRootPath = path
			}
			return io.EOF // 使用EOF作为找到结果的信号
		}
		return nil
	})
	if err == io.EOF {
		err = nil
	}
	if appName == "" {
		return "", "", fmt.Errorf("未找到.app文件")
	}
	return appName, appContentRootPath, err
}

// addFilesToApp 从指定文件夹添加文件到.app目录
func addFilesToApp(appPath string, addFilesPath string) error {
	if addFilesPath == "" {
		return nil
	}

	// 检查源文件夹是否存在
	if !utility.IsExist(addFilesPath) {
		utility.LogPrintWarning(fmt.Sprintf("要添加的文件夹不存在，跳过添加文件: %s", addFilesPath))
		return nil
	}

	if !utility.IsDir(addFilesPath) {
		return fmt.Errorf("指定的路径不是文件夹: %s", addFilesPath)
	}

	utility.LogPrintInfo(fmt.Sprintf("开始添加文件从: %s 到 %s", addFilesPath, appPath))

	// 遍历源文件夹中的所有文件和文件夹
	err := filepath.Walk(addFilesPath, func(srcPath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过根目录本身
		if srcPath == addFilesPath {
			return nil
		}

		// 忽略以点开头的隐藏文件和文件夹
		if strings.HasPrefix(info.Name(), ".") {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 计算相对路径
		relPath, err := filepath.Rel(addFilesPath, srcPath)
		if err != nil {
			return fmt.Errorf("计算相对路径失败: %w", err)
		}

		// 构建目标路径
		targetPath := filepath.Join(appPath, relPath)

		if info.IsDir() {
			// 检查是否为特殊文件夹类型
			if isSpecialBundle(relPath) {
				return handleSpecialBundle(srcPath, targetPath, relPath)
			}
			return handleDirectoryAdd(srcPath, targetPath, relPath)
		} else {
			return handleFileAdd(srcPath, targetPath, relPath)
		}
	})

	if err != nil {
		return fmt.Errorf("添加文件失败: %w", err)
	}

	utility.LogPrintSuccess("文件添加完成")
	return nil
}

// deleteFilesFromApp 从.app目录中删除指定的文件或文件夹
func deleteFilesFromApp(appPath string, deleteFilePaths string) error {
	if deleteFilePaths == "" {
		return nil
	}

	utility.LogPrintInfo("开始删除指定的文件或文件夹...")

	// 分割路径字符串
	paths := strings.Split(deleteFilePaths, ",")

	for _, path := range paths {
		// 去除空格
		path = strings.TrimSpace(path)
		if path == "" {
			continue
		}

		// 构建完整路径
		fullPath := filepath.Join(appPath, path)

		// 检查文件或目录是否存在
		if !utility.IsExist(fullPath) {
			utility.LogPrintWarning(fmt.Sprintf("文件或目录不存在，跳过删除: %s", path))
			continue
		}

		// 判断是文件还是目录
		isDir := utility.IsDir(fullPath)

		// 执行删除操作
		var err error
		if isDir {
			utility.LogPrintInfo(fmt.Sprintf("删除目录: %s", path))
			err = os.RemoveAll(fullPath)
		} else {
			utility.LogPrintInfo(fmt.Sprintf("删除文件: %s", path))
			err = os.Remove(fullPath)
		}

		if err != nil {
			utility.LogPrintWarning(fmt.Sprintf("删除失败: %s, 错误: %v", path, err))
		} else {
			utility.LogPrintSuccess(fmt.Sprintf("成功删除: %s", path))
		}
	}

	return nil
}

// handleDirectoryAdd 处理普通文件夹添加
func handleDirectoryAdd(srcPath, targetPath, relPath string) error {
	// 普通文件夹处理
	if utility.IsExist(targetPath) {
		// 目标文件夹已存在，不需要特殊处理，继续遍历内部文件
		utility.LogPrintInfo(fmt.Sprintf("目标文件夹已存在，将遍历内部文件: %s", relPath))
		return nil
	} else {
		// 目标文件夹不存在，创建文件夹
		if err := os.MkdirAll(targetPath, 0755); err != nil {
			return fmt.Errorf("创建文件夹失败: %s, 错误: %w", relPath, err)
		}
		utility.LogPrintInfo(fmt.Sprintf("添加文件夹: %s -> %s", srcPath, targetPath))
		return nil
	}
}

// handleFileAdd 处理文件添加
func handleFileAdd(srcPath, targetPath, relPath string) error {
	// 检查目标文件是否已存在
	if utility.IsExist(targetPath) {
		// 文件已存在，强制覆盖
		if err := copyFileForAdd(srcPath, targetPath); err != nil {
			return fmt.Errorf("覆盖文件失败: %s, 错误: %w", relPath, err)
		}
		utility.LogPrintInfo(fmt.Sprintf("覆盖文件: %s -> %s", srcPath, targetPath))
	} else {
		// 文件不存在，直接复制
		// 确保目标目录存在
		targetDir := filepath.Dir(targetPath)
		if !utility.IsExist(targetDir) {
			if err := os.MkdirAll(targetDir, 0755); err != nil {
				return fmt.Errorf("创建目标目录失败: %s, 错误: %w", targetDir, err)
			}
		}

		if err := copyFileForAdd(srcPath, targetPath); err != nil {
			return fmt.Errorf("添加文件失败: %s, 错误: %w", relPath, err)
		}
		utility.LogPrintInfo(fmt.Sprintf("添加文件: %s -> %s", srcPath, targetPath))
	}
	return nil
}

// isSpecialBundle 检查是否为特殊文件夹类型
func isSpecialBundle(path string) bool {
	// 获取文件夹名称
	name := filepath.Base(path)

	// 检查是否以特殊后缀结尾，.bundle 和 .lproj先支持部分替换，后续如果有问题再只支持全量替换
	specialSuffixes := []string{".framework"}
	for _, suffix := range specialSuffixes {
		if strings.HasSuffix(name, suffix) {
			return true
		}
	}
	return false
}

// handleSpecialBundle 处理特殊文件夹类型（.bundle、.framework、.lproj）
func handleSpecialBundle(srcPath, targetPath, relPath string) error {
	// 对于特殊文件夹，直接整体替换
	if utility.IsExist(targetPath) {
		// 删除现有的特殊文件夹
		if err := os.RemoveAll(targetPath); err != nil {
			return fmt.Errorf("删除现有特殊文件夹失败: %s, 错误: %w", relPath, err)
		}
	}

	// 确保目标目录的父目录存在
	targetDir := filepath.Dir(targetPath)
	if !utility.IsExist(targetDir) {
		if err := os.MkdirAll(targetDir, 0755); err != nil {
			return fmt.Errorf("创建目标目录失败: %s, 错误: %w", targetDir, err)
		}
	}

	// 复制整个特殊文件夹
	if err := copyDirRecursivelyForAdd(srcPath, targetPath); err != nil {
		return fmt.Errorf("复制特殊文件夹失败: %s, 错误: %w", relPath, err)
	}

	utility.LogPrintInfo(fmt.Sprintf("替换特殊文件夹: %s -> %s", srcPath, targetPath))

	// 返回 filepath.SkipDir 来跳过遍历这个特殊文件夹的内部文件
	return filepath.SkipDir
}

// copyFileForAdd 复制文件（用于添加文件功能）
func copyFileForAdd(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	srcInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	return os.Chmod(dst, srcInfo.Mode())
}

// copyDirRecursivelyForAdd 递归复制目录（用于添加文件功能）
func copyDirRecursivelyForAdd(src, dst string) error {
	srcInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	// 创建目标目录
	if err := os.MkdirAll(dst, srcInfo.Mode()); err != nil {
		return err
	}

	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		// 跳过以点开头的隐藏文件和文件夹
		if strings.HasPrefix(entry.Name(), ".") {
			continue
		}

		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			if err := copyDirRecursivelyForAdd(srcPath, dstPath); err != nil {
				return err
			}
		} else {
			if err := copyFileForAdd(srcPath, dstPath); err != nil {
				return err
			}
		}
	}

	return nil
}

// resizeAndSaveImage 调整图片尺寸并保存，支持智能裁剪和缩放
func resizeAndSaveImage(srcPath, dstPath string, width, height int) error {
	// 获取原图尺寸
	srcWidth, srcHeight, err := getImageDimensions(srcPath)
	if err != nil {
		return fmt.Errorf("获取原图尺寸失败: %w", err)
	}

	// 计算原图和目标图的宽高比
	srcRatio := float64(srcWidth) / float64(srcHeight)
	dstRatio := float64(width) / float64(height)

	// 如果比例相同（允许小的误差），直接等比缩放
	if abs(srcRatio-dstRatio) < 0.01 {
		return resizeImageDirect(srcPath, dstPath, width, height)
	}

	// 比例不同，需要先裁剪再缩放
	return resizeImageWithCrop(srcPath, dstPath, srcWidth, srcHeight, width, height)
}

// getImageDimensions 获取图片尺寸
func getImageDimensions(imagePath string) (int, int, error) {
	cmd := exec.Command("sips", "-g", "pixelWidth", "-g", "pixelHeight", imagePath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return 0, 0, fmt.Errorf("获取图片尺寸失败: %s, %w", string(output), err)
	}

	var width, height int
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "pixelWidth:") {
			fmt.Sscanf(line, "  pixelWidth: %d", &width)
		} else if strings.Contains(line, "pixelHeight:") {
			fmt.Sscanf(line, "  pixelHeight: %d", &height)
		}
	}

	if width == 0 || height == 0 {
		return 0, 0, fmt.Errorf("无法解析图片尺寸")
	}

	return width, height, nil
}

// resizeImageDirect 直接等比缩放
func resizeImageDirect(srcPath, dstPath string, width, height int) error {
	cmd := exec.Command("sips", "-z", fmt.Sprintf("%d", height), fmt.Sprintf("%d", width), srcPath, "--out", dstPath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("等比缩放失败: %s, %w", string(output), err)
	}
	return nil
}

// resizeImageWithCrop 先裁剪再缩放
func resizeImageWithCrop(srcPath, dstPath string, srcWidth, srcHeight, dstWidth, dstHeight int) error {
	// 计算目标宽高比
	dstRatio := float64(dstWidth) / float64(dstHeight)

	// 计算裁剪尺寸（保持目标比例）
	var cropWidth, cropHeight int
	if float64(srcWidth)/float64(srcHeight) > dstRatio {
		// 原图更宽，按高度计算裁剪宽度
		cropHeight = srcHeight
		cropWidth = int(float64(srcHeight) * dstRatio)
	} else {
		// 原图更高，按宽度计算裁剪高度
		cropWidth = srcWidth
		cropHeight = int(float64(srcWidth) / dstRatio)
	}

	// 计算居中裁剪的起始位置
	cropX := (srcWidth - cropWidth) / 2
	cropY := (srcHeight - cropHeight) / 2

	// 创建临时文件用于裁剪
	tempPath := dstPath + ".temp"
	defer os.Remove(tempPath)

	// 先裁剪到目标比例
	cropCmd := exec.Command("sips",
		"--cropToHeightWidth", fmt.Sprintf("%d", cropHeight), fmt.Sprintf("%d", cropWidth),
		"--cropOffset", fmt.Sprintf("%d", cropY), fmt.Sprintf("%d", cropX),
		srcPath, "--out", tempPath)

	output, err := cropCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("裁剪图片失败: %s, %w", string(output), err)
	}

	// 再缩放到目标尺寸
	resizeCmd := exec.Command("sips", "-z", fmt.Sprintf("%d", dstHeight), fmt.Sprintf("%d", dstWidth), tempPath, "--out", dstPath)
	output, err = resizeCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("缩放图片失败: %s, %w", string(output), err)
	}

	return nil
}

// abs 计算浮点数绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
