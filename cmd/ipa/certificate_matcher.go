package ipa

import (
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/xml"
	"fmt"
	"os/exec"
	"regexp"
	"strings"
)

// plist 结构体，用于解析 mobileprovision 文件中的开发者证书
type provisionPlist struct {
	XMLName xml.Name `xml:"plist"`
	Dict    struct {
		Items []interface{} `xml:",any"`
	} `xml:"dict"`
}

// 本地证书身份信息
type Identity struct {
	SHA1 string // SHA1 指纹
	Name string // 证书名称
}

// 从 .mobileprovision 文件中提取证书 DER 数据
func extractCertificatesFromProfile(profilePath string) ([][]byte, error) {
	cmd := exec.Command("security", "cms", "-D", "-i", profilePath)
	out, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("解析 mobileprovision 文件失败: %w", err)
	}

	// 使用正则表达式提取 DeveloperCertificates 数组中的 data 元素
	// 使用 (?s) 标志让 . 匹配换行符
	re := regexp.MustCompile(`(?s)<key>DeveloperCertificates</key>\s*<array>(.*?)</array>`)
	matches := re.FindSubmatch(out)
	if len(matches) < 2 {
		return nil, fmt.Errorf("未找到 DeveloperCertificates 数组")
	}

	// 提取所有 <data> 标签中的内容
	dataRe := regexp.MustCompile(`(?s)<data>(.*?)</data>`)
	dataMatches := dataRe.FindAllSubmatch(matches[1], -1)

	if len(dataMatches) == 0 {
		return nil, fmt.Errorf("DeveloperCertificates 数组中未找到证书数据")
	}

	var certs [][]byte
	for _, match := range dataMatches {
		if len(match) < 2 {
			continue
		}

		b64 := strings.TrimSpace(string(match[1]))
		// 移除换行符和空白字符
		b64 = strings.ReplaceAll(b64, "\n", "")
		b64 = strings.ReplaceAll(b64, "\t", "")
		b64 = strings.ReplaceAll(b64, " ", "")

		der, err := base64.StdEncoding.DecodeString(b64)
		if err != nil {
			return nil, fmt.Errorf("解码证书数据失败: %w", err)
		}
		certs = append(certs, der)
	}

	return certs, nil
}

// 计算证书的 SHA1 指纹（与 security find-identity 命令结果一致）
func calculateSHA1Fingerprint(der []byte) (string, error) {
	cert, err := x509.ParseCertificate(der)
	if err != nil {
		return "", fmt.Errorf("解析证书失败: %w", err)
	}

	h := sha1.Sum(cert.Raw)
	return strings.ToUpper(hex.EncodeToString(h[:])), nil
}

// 获取本地钥匙串中的代码签名身份列表
func getLocalCodeSigningIdentities() ([]Identity, error) {
	cmd := exec.Command("security", "find-identity", "-v", "-p", "codesigning")
	out, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取本地代码签名身份失败: %w", err)
	}

	// 解析输出，每行格式类似：
	//  1) ABCD1234567890ABCDEF1234567890ABCDEF1234 "Apple Development: xxx (XXXXXXXXXX)"
	re := regexp.MustCompile(`\d+\) ([0-9A-F]{40}) "(.+)"`)
	lines := strings.Split(string(out), "\n")

	var identities []Identity
	for _, line := range lines {
		matches := re.FindStringSubmatch(line)
		if len(matches) == 3 {
			identities = append(identities, Identity{
				SHA1: strings.ToUpper(matches[1]),
				Name: matches[2],
			})
		}
	}

	return identities, nil
}

// MatchCertificateResult 证书匹配结果
type MatchCertificateResult struct {
	Found        bool   // 是否找到匹配的证书
	IdentityName string // 匹配的身份名称
	SHA1         string // 证书 SHA1 指纹
	Error        string // 错误信息（如果有）
}

// MatchCertificateFromProfile 根据 mobileprovision 文件匹配本地证书
// 返回第一个匹配到的证书信息
func MatchCertificateFromProfile(profilePath string) (*MatchCertificateResult, error) {
	// 提取 mobileprovision 文件中的证书
	certs, err := extractCertificatesFromProfile(profilePath)
	if err != nil {
		return &MatchCertificateResult{
			Found: false,
			Error: fmt.Sprintf("提取证书失败: %v", err),
		}, err
	}

	if len(certs) == 0 {
		return &MatchCertificateResult{
			Found: false,
			Error: "mobileprovision 文件中未找到证书",
		}, fmt.Errorf("mobileprovision 文件中未找到证书")
	}

	// 获取本地代码签名身份
	localIdentities, err := getLocalCodeSigningIdentities()
	if err != nil {
		return &MatchCertificateResult{
			Found: false,
			Error: fmt.Sprintf("获取本地身份失败: %v", err),
		}, err
	}

	if len(localIdentities) == 0 {
		return &MatchCertificateResult{
			Found: false,
			Error: "本地未找到代码签名身份",
		}, fmt.Errorf("本地未找到代码签名身份")
	}

	// 遍历证书，寻找匹配的本地身份
	for _, cert := range certs {
		fingerprint, err := calculateSHA1Fingerprint(cert)
		if err != nil {
			continue // 跳过无法解析的证书
		}

		// 在本地身份中查找匹配的 SHA1 指纹
		for _, identity := range localIdentities {
			if identity.SHA1 == fingerprint {
				return &MatchCertificateResult{
					Found:        true,
					IdentityName: identity.Name,
					SHA1:         fingerprint,
				}, nil
			}
		}
	}

	// 未找到匹配的证书
	return &MatchCertificateResult{
		Found: false,
		Error: "未找到匹配的本地证书身份",
	}, fmt.Errorf("未找到匹配的本地证书身份")
}

// GetAllMatchingCertificates 获取所有匹配的证书信息
func GetAllMatchingCertificates(profilePath string) ([]MatchCertificateResult, error) {
	// 提取 mobileprovision 文件中的证书
	certs, err := extractCertificatesFromProfile(profilePath)
	if err != nil {
		return nil, fmt.Errorf("提取证书失败: %v", err)
	}

	// 获取本地代码签名身份
	localIdentities, err := getLocalCodeSigningIdentities()
	if err != nil {
		return nil, fmt.Errorf("获取本地身份失败: %v", err)
	}

	var results []MatchCertificateResult

	// 遍历所有证书
	for i, cert := range certs {
		fingerprint, err := calculateSHA1Fingerprint(cert)
		if err != nil {
			results = append(results, MatchCertificateResult{
				Found: false,
				Error: fmt.Sprintf("证书 %d 解析失败: %v", i+1, err),
			})
			continue
		}

		// 查找匹配的本地身份
		found := false
		for _, identity := range localIdentities {
			if identity.SHA1 == fingerprint {
				results = append(results, MatchCertificateResult{
					Found:        true,
					IdentityName: identity.Name,
					SHA1:         fingerprint,
				})
				found = true
				break
			}
		}

		if !found {
			results = append(results, MatchCertificateResult{
				Found: false,
				SHA1:  fingerprint,
				Error: fmt.Sprintf("证书 %d 未找到匹配的本地身份", i+1),
			})
		}
	}

	return results, nil
}

// 获取本地钥匙串中的3rd Party Mac Developer Installer类型签名证书
func getLocal3rdPartyMacDeveloperInstallerCert(teamIdentity string) (*MatchCertificateResult, error) {
	cmd := exec.Command("security", "find-identity", "-v", "-p", "macappstore")
	out, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("获取本地代码签名身份失败: %w", err)
	}

	// 解析输出，每行格式类似：
	//  1) ABCD1234567890ABCDEF1234567890ABCDEF1234 "Apple Development: xxx (XXXXXXXXXX)"
	re := regexp.MustCompile(`\d+\) ([0-9A-F]{40}) "(.+)"`)
	lines := strings.Split(string(out), "\n")

	for _, line := range lines {
		matches := re.FindStringSubmatch(line)
		if len(matches) == 3 {
			certSHA1 := strings.ToUpper(matches[1])
			certName := matches[2]
			if strings.Contains(certName, "3rd Party Mac Developer Installer:") && strings.Contains(certName, teamIdentity) {
				certificateResult := &MatchCertificateResult{
					Found:        true,
					IdentityName: certName,
					SHA1:         certSHA1,
				}

				return certificateResult, nil
			}
		}
	}

	return nil, fmt.Errorf("未获取到[%s]对应的3rd Party Mac Developer Installer证书", teamIdentity)
}
