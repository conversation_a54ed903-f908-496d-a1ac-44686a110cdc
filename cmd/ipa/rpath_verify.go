package ipa

import (
	"archive/zip"
	"fmt"
	"io"
	"onetools/cmd/utility"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
)

// RpathInfo 存储rpath相关信息
type RpathInfo struct {
	AppPath           string   // .app目录路径
	MainExecutable    string   // 主可执行文件路径
	LoadedLibraries   []string // 加载的动态库列表
	RpathList         []string // LC_RPATH列表
	CustomLibraries   []string // 自定义动态库列表
	MissingRpaths     []string // 缺失的rpath
	UnloadedLibraries []string // 未被正确加载的动态库
	LoadedFrameworks  []string // 已正确加载的Framework库
	IsUnityProject    bool     // 是否为Unity项目
}

// VerifyAndFixRpathExe 验证和修复ipa中的rpath配置
func VerifyAndFixRpathExe(ipaPath string, platform utility.PlatformOS) error {
	utility.LogPrintInfo(fmt.Sprintf("开始验证rpath配置: %s", ipaPath))

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "rpath_verify_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// 解压IPA文件
	if strings.HasSuffix(ipaPath, ".ipa") || strings.HasSuffix(ipaPath, ".zip") {
		utility.LogPrintInfo("解压IPA文件...")
		if err := UnzipIPAExe(ipaPath, tempDir); err != nil {
			return fmt.Errorf("解压IPA文件失败: %w", err)
		}
	} else {
		// .app文件直接拷贝到temp目录
		// copyDirRecursivelyForAdd(ipaPath, tempDir)
		appName := filepath.Base(ipaPath)
		tempDir = filepath.Join(tempDir, appName)
		copyDirRecursively(ipaPath, tempDir)
	}

	// 查找.app目录
	appPath, err := findAppDirectory(tempDir, platform)
	if err != nil {
		return fmt.Errorf("查找.app目录失败: %w", err)
	}

	utility.LogPrintInfo(fmt.Sprintf("找到应用目录: %s", filepath.Base(appPath)))

	// 分析rpath信息
	rpathInfo, err := analyzeRpathInfo(appPath, platform)
	if err != nil {
		return fmt.Errorf("分析rpath信息失败: %w", err)
	}

	// 打印分析结果
	printRpathAnalysis(rpathInfo)
	return nil
}

// findAppDirectory 查找.app目录
func findAppDirectory(tempDir string, platform utility.PlatformOS) (string, error) {
	payloadDir := tempDir

	if strings.HasSuffix(payloadDir, ".app") {
		return payloadDir, nil
	}

	if platform == utility.PlatformOS_IOS {
		payloadDir = filepath.Join(tempDir, "Payload")
		if !utility.IsDir(payloadDir) {
			return "", fmt.Errorf("未找到Payload目录")
		}
	}

	entries, err := os.ReadDir(payloadDir)
	if err != nil {
		return "", fmt.Errorf("读取Payload目录失败: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() && strings.HasSuffix(entry.Name(), ".app") {
			return filepath.Join(payloadDir, entry.Name()), nil
		}
	}

	return "", fmt.Errorf("未找到.app目录")
}

// analyzeRpathInfo 分析rpath信息
func analyzeRpathInfo(appPath string, platform utility.PlatformOS) (*RpathInfo, error) {
	info := &RpathInfo{
		AppPath: appPath,
	}

	// 确定主可执行文件
	mainExec, isUnity, err := determineMainExecutable(appPath, platform)
	if err != nil {
		return nil, fmt.Errorf("确定主可执行文件失败: %w", err)
	}

	info.MainExecutable = mainExec
	info.IsUnityProject = isUnity

	// 获取加载的动态库列表
	loadedLibs, err := getLoadedLibraries(mainExec)
	if err != nil {
		return nil, fmt.Errorf("获取加载的动态库失败: %w", err)
	}
	info.LoadedLibraries = loadedLibs

	// 获取LC_RPATH列表
	rpathList, err := getRpathList(mainExec)
	if err != nil {
		return nil, fmt.Errorf("获取LC_RPATH列表失败: %w", err)
	}
	info.RpathList = rpathList

	// 扫描自定义动态库
	customLibs, err := scanCustomLibraries(appPath, platform)
	if err != nil {
		return nil, fmt.Errorf("扫描自定义动态库失败: %w", err)
	}
	info.CustomLibraries = customLibs

	// 检查缺失的rpath
	missingRpaths := checkMissingRpaths(info, platform)
	info.MissingRpaths = missingRpaths

	// 检查Frameworks中的动态库加载状态
	unloadedLibs, loadedFrameworks := checkFrameworksLoading(info, platform)
	info.UnloadedLibraries = unloadedLibs
	info.LoadedFrameworks = loadedFrameworks

	return info, nil
}

// determineMainExecutable 确定主可执行文件
func determineMainExecutable(appPath string, platform utility.PlatformOS) (string, bool, error) {
	var mainExecPath string
	if platform == utility.PlatformOS_OSX {
		// 检查是否为Unity项目
		unityPlyerPath := filepath.Join(appPath, "Contents", "Frameworks", "UnityPlayer.dylib")
		if utility.IsExist(unityPlyerPath) {
			return unityPlyerPath, true, nil
		}

		// 获取应用名称
		appName := strings.TrimSuffix(filepath.Base(appPath), ".app")
		mainExecPath = filepath.Join(appPath, "Contents", "MacOS", appName)
	} else {
		// 检查是否为Unity项目
		unityFrameworkPath := filepath.Join(appPath, "Frameworks", "UnityFramework.framework", "UnityFramework")
		if utility.IsExist(unityFrameworkPath) {
			return unityFrameworkPath, true, nil
		}

		// 获取应用名称
		appName := strings.TrimSuffix(filepath.Base(appPath), ".app")
		mainExecPath = filepath.Join(appPath, appName)
	}

	if !utility.IsExist(mainExecPath) {
		return "", false, fmt.Errorf("未找到主可执行文件: %s", mainExecPath)
	}

	return mainExecPath, false, nil
}

// getLoadedLibraries 获取加载的动态库列表
func getLoadedLibraries(execPath string) ([]string, error) {
	cmd := exec.Command("otool", "-L", execPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行otool命令失败: %w", err)
	}

	var libraries []string
	lines := strings.Split(string(output), "\n")

	// 跳过第一行（文件路径）
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// 提取库路径（去掉版本信息）
		parts := strings.Fields(line)
		if len(parts) > 0 {
			libPath := parts[0]
			libraries = append(libraries, libPath)
		}
	}

	return libraries, nil
}

// getRpathList 获取LC_RPATH列表
func getRpathList(execPath string) ([]string, error) {
	cmd := exec.Command("otool", "-l", execPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行otool命令失败: %w", err)
	}

	var rpaths []string
	lines := strings.Split(string(output), "\n")

	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if strings.Contains(line, "LC_RPATH") {
			// 查找下一行的path信息
			for j := i + 1; j < len(lines) && j < i+5; j++ {
				pathLine := strings.TrimSpace(lines[j])
				if strings.HasPrefix(pathLine, "path ") {
					// 提取路径，格式通常是 "path /path/to/lib (offset 12)"
					re := regexp.MustCompile(`path\s+([^\s]+)`)
					matches := re.FindStringSubmatch(pathLine)
					if len(matches) > 1 {
						rpaths = append(rpaths, matches[1])
					}
					break
				}
			}
		}
	}

	return rpaths, nil
}

func scanPathsWithDynamicLib(appPath string, platform utility.PlatformOS) []string {
	var scanPaths []string
	if platform == utility.PlatformOS_OSX {
		contentPath := filepath.Join(appPath, "Contents")
		scanPaths = append(scanPaths, filepath.Join(contentPath, "Frameworks"))
		scanPaths = append(scanPaths, filepath.Join(contentPath, "UE"))
	} else {
		scanPaths = append(scanPaths, filepath.Join(appPath, "Frameworks"))
	}
	return scanPaths
}

// scanCustomLibraries 扫描自定义动态库
func scanCustomLibraries(appPath string, platform utility.PlatformOS) ([]string, error) {
	var customLibs []string

	scanPaths := scanPathsWithDynamicLib(appPath, platform)
	for _, frameworksPath := range scanPaths {
		if !utility.IsDir(frameworksPath) {
			continue
		}

		filepath.Walk(frameworksPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 收集.dylib文件
			if !info.IsDir() && strings.HasSuffix(info.Name(), ".dylib") {
				relPath, _ := filepath.Rel(appPath, path)
				customLibs = append(customLibs, relPath)
			}

			// 收集.framework中的可执行文件
			if info.IsDir() && strings.HasSuffix(info.Name(), ".framework") {
				frameworkName := strings.TrimSuffix(info.Name(), ".framework")
				execPath := filepath.Join(path, frameworkName)
				if utility.IsExist(execPath) {
					relPath, _ := filepath.Rel(appPath, execPath)
					customLibs = append(customLibs, relPath)
				}
			}
			return nil
		})
	}

	return customLibs, nil
}

// checkMissingRpaths 检查缺失的rpath和未正确加载的动态库
func checkMissingRpaths(info *RpathInfo, platform utility.PlatformOS) []string {
	var missingRpaths []string

	// 检查每个自定义动态库是否有对应的rpath
	for _, customLib := range info.CustomLibraries {
		libDir := filepath.Dir(customLib)
		// 检查是否已存在对应的rpath
		found := false
		// 转换为rpath格式
		var expectedRpath string

		if platform == utility.PlatformOS_OSX {

			//忽略macOS平台Contents/UE/OneSDKDemo/Binaries/Mac/下的dylib忽略
			mainExecutableName := filepath.Base(info.MainExecutable)
			ignoreDirPath := "Contents/UE/" + mainExecutableName + "/Binaries/Mac"
			if strings.HasSuffix(libDir, ignoreDirPath) {
				utility.LogPrintInfo(fmt.Sprintf("[SKIP] 跳过该路径下动态库rpath处理: %s", libDir))
				found = true
			} else {
				if strings.HasSuffix(libDir, ".framework") {
					libDir = filepath.Dir(libDir)
				}
				expectedRpath = "@loader_path/../../" + libDir
				executableRpath := "@executable_path/../../" + libDir
				for _, existingRpath := range info.RpathList {
					if existingRpath == expectedRpath ||
						executableRpath == expectedRpath {
						found = true
						break
					}
				}
			}
		} else {
			if strings.HasPrefix(libDir, "Frameworks") {
				expectedRpath = "@executable_path/Frameworks"
			} else {
				expectedRpath = "@executable_path/" + libDir
			}

			for _, existingRpath := range info.RpathList {
				if existingRpath == expectedRpath ||
					existingRpath == "@executable_path/Frameworks" ||
					existingRpath == "@loader_path/Frameworks" {
					found = true
					break
				}
			}
		}

		if !found {
			// 避免重复添加
			duplicate := false
			for _, missing := range missingRpaths {
				if missing == expectedRpath {
					duplicate = true
					break
				}
			}
			if !duplicate {
				missingRpaths = append(missingRpaths, expectedRpath)
			}
		}
	}

	return missingRpaths
}

// checkFrameworksLoading 检查Frameworks目录中的动态库是否被主进程正确加载
func checkFrameworksLoading(info *RpathInfo, platform utility.PlatformOS) ([]string, []string) {
	var unloadedLibraries []string
	var loadedLibraries []string

	scanPaths := scanPathsWithDynamicLib(info.AppPath, platform)
	for _, frameworksPath := range scanPaths {
		if !utility.IsDir(frameworksPath) {
			continue
		}

		// 遍历Frameworks目录
		filepath.Walk(frameworksPath, func(path string, fileInfo os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			var libName string
			var expectedRpathRef string
			var libType string

			// 处理.dylib文件
			if !fileInfo.IsDir() && strings.HasSuffix(fileInfo.Name(), ".dylib") {
				// 跳过libswift开头且以.dylib结尾的系统动态库
				if strings.HasPrefix(fileInfo.Name(), "libswift") && strings.HasSuffix(fileInfo.Name(), ".dylib") {
					utility.LogPrintInfo(fmt.Sprintf("[SKIP] 跳过系统Swift库: %s", fileInfo.Name()))
					return nil
				}

				//忽略macOS平台Contents/UE/OneSDKDemo/Binaries/Mac/下的dylib忽略
				if platform == utility.PlatformOS_OSX {
					mainExecutableName := filepath.Base(info.MainExecutable)
					ignoreDirPath := "Contents/UE/" + mainExecutableName + "/Binaries/Mac"
					dylibDirPath := filepath.Dir(path)

					if strings.HasSuffix(dylibDirPath, ignoreDirPath) {
						utility.LogPrintInfo(fmt.Sprintf("[SKIP] 跳过该路径下动态库处理: %s", path))
						return nil
					}
				}
				libName = fileInfo.Name()
				expectedRpathRef = "@rpath/" + libName
				libType = "dylib"
			}

			// 处理.framework目录
			if fileInfo.IsDir() && strings.HasSuffix(fileInfo.Name(), ".framework") {
				frameworkName := strings.TrimSuffix(fileInfo.Name(), ".framework")
				execPath := filepath.Join(path, frameworkName)

				// 检查framework内的可执行文件是否存在
				if utility.IsExist(execPath) {
					// 检查是否为可执行文件（不是符号链接或其他类型）
					if execFileInfo, err := os.Stat(execPath); err == nil {
						if execFileInfo.Mode().IsRegular() && (execFileInfo.Mode()&0111) != 0 {
							libName = frameworkName
							expectedRpathRef = "@rpath/" + frameworkName + ".framework/" + frameworkName
							libType = "framework"
						}
					}
				}
			}

			// 如果找到了需要检查的库
			if libName != "" && expectedRpathRef != "" {
				// 检查是否在主进程的加载列表中
				isLoaded := false
				for _, loadedLib := range info.LoadedLibraries {
					// utility.LogPrintInfo(fmt.Sprintf("检查%s: %s", loadedLib, libName))
					// 精确匹配或模糊匹配
					if loadedLib == expectedRpathRef ||
						strings.Contains(loadedLib, libName) ||
						(libType == "framework" && strings.Contains(loadedLib, fmt.Sprintf("%s.framework/%s", libName, libName))) ||
						(libType == "dylib" && strings.Contains(loadedLib, strings.TrimSuffix(libName, "dylib"))) {
						//最后libType == "dylib的判断，是防止有多个不同版本的dylib库引入
						isLoaded = true
						break
					}
				}

				resultMsg := fmt.Sprintf("%s (%s) -> %s", path, libType, expectedRpathRef)

				if isLoaded {
					loadedLibraries = append(loadedLibraries, resultMsg)
					// utility.LogPrintInfo(fmt.Sprintf("✓ 已加载: %s", resultMsg))
				} else {
					unloadedLibraries = append(unloadedLibraries, resultMsg)
					// utility.LogPrintError(fmt.Sprintf("✗ 未加载: %s", resultMsg))
				}
			}

			return nil
		})
	}

	return unloadedLibraries, loadedLibraries
}

// printRpathAnalysis 打印rpath分析结果
func printRpathAnalysis(info *RpathInfo) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("RPATH 配置分析结果")
	fmt.Println(strings.Repeat("=", 60))

	// 打印基本信息
	fmt.Printf("应用目录: %s\n", filepath.Base(info.AppPath))
	fmt.Printf("主可执行文件: %s\n", filepath.Base(info.MainExecutable))
	if info.IsUnityProject {
		utility.LogPrintInfo("检测到Unity项目，使用UnityFramework作为主可执行文件")
	}

	// 打印加载的动态库
	fmt.Println("\n1. 主要加载的动态库:")
	if len(info.LoadedLibraries) == 0 {
		utility.LogPrintWarning("未找到加载的动态库")
	} else {
		for i, lib := range info.LoadedLibraries {
			fmt.Printf("   %d. %s\n", i+1, lib)
		}
	}

	// 打印LC_RPATH列表
	fmt.Println("\n2. LC_RPATH 路径:")
	if len(info.RpathList) == 0 {
		utility.LogPrintWarning("未找到LC_RPATH配置")
	} else {
		for i, rpath := range info.RpathList {
			fmt.Printf("   %d. %s\n", i+1, rpath)
		}
	}

	// 打印自定义动态库
	fmt.Println("\n3. 自定义动态库:")
	if len(info.CustomLibraries) == 0 {
		utility.LogPrintInfo("未找到自定义动态库")
	} else {
		for i, lib := range info.CustomLibraries {
			fmt.Printf("   %d. %s\n", i+1, lib)
		}
	}

	// 打印缺失的rpath
	fmt.Println("\n4. 缺失的RPATH配置:")
	if len(info.MissingRpaths) == 0 {
		utility.LogPrintSuccess("所有自定义动态库的rpath配置正确")
	} else {
		for i, rpath := range info.MissingRpaths {
			utility.LogPrintError(fmt.Sprintf("%d. %s", i+1, rpath))
		}
	}

	// 打印未正确加载的动态库
	fmt.Println("\n5. 未正确加载的动态库:")
	if len(info.UnloadedLibraries) == 0 {
		utility.LogPrintSuccess("所有Frameworks中的动态库都已被主进程正确加载")
	} else {
		utility.LogPrintWarning(fmt.Sprintf("发现 %d 个未被主进程加载的动态库:", len(info.UnloadedLibraries)))
		for i, lib := range info.UnloadedLibraries {
			utility.LogPrintError(fmt.Sprintf("%d. %s", i+1, lib))
		}
		utility.LogPrintWarning("这些库可能导致运行时加载失败，建议检查代码中的动态库引用")
	}

	fmt.Println(strings.Repeat("=", 60))
}

// fixRpathConfiguration 修复rpath配置
func fixRpathConfiguration(info *RpathInfo) error {
	if len(info.MissingRpaths) == 0 {
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("需要添加 %d 个rpath配置", len(info.MissingRpaths)))

	for _, rpath := range info.MissingRpaths {
		utility.LogPrintInfo(fmt.Sprintf("添加rpath: %s", rpath))

		// 使用install_name_tool添加rpath
		cmd := exec.Command("install_name_tool", "-add_rpath", rpath, info.MainExecutable)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("添加rpath失败 %s: %w", rpath, err)
		}

		utility.LogPrintSuccess(fmt.Sprintf("成功添加rpath: %s", rpath))
	}

	return nil
}

// fixUnloadedLibraries 修复未加载的动态库，使用optool将它们添加到主进程的Load command中
func fixUnloadedLibraries(info *RpathInfo) error {
	if len(info.UnloadedLibraries) == 0 {
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("需要修复 %d 个未加载的动态库", len(info.UnloadedLibraries)))

	// 确保optool工具可用
	optoolPath, err := ensureOptoolAvailable()
	if err != nil {
		return fmt.Errorf("获取optool工具失败: %w", err)
	}

	for _, unloadedLib := range info.UnloadedLibraries {
		// 解析库信息，格式: "OpenDebugLog (framework) -> @rpath/OpenDebugLog/OpenDebugLog"
		parts := strings.Split(unloadedLib, " -> ")
		if len(parts) != 2 {
			utility.LogPrintWarning(fmt.Sprintf("跳过格式不正确的库信息: %s", unloadedLib))
			continue
		}

		libInfo := parts[0]
		expectedRpathRef := parts[1]

		// 提取库名和类型
		libNameParts := strings.Split(libInfo, " (")
		if len(libNameParts) != 2 {
			utility.LogPrintWarning(fmt.Sprintf("跳过格式不正确的库信息: %s", libInfo))
			continue
		}

		libPath := libNameParts[0]
		libType := strings.TrimSuffix(libNameParts[1], ")")

		utility.LogPrintInfo(fmt.Sprintf("修复未加载的%s: %s", libType, libPath))

		// 构建实际的库文件路径
		actualLibPath := libPath
		if libType == "framework" {
			// 对于framework，路径格式为: Frameworks/LibName.framework/LibName
			// 取最后一段 (LibName.framework)
			libBase := filepath.Base(libPath)
			// 去掉扩展名 ".framework"
			libBaseName := strings.TrimSuffix(libBase, ".framework")
			actualLibPath = filepath.Join(libPath, libBaseName)
		}

		// 检查库文件是否存在
		if !utility.IsExist(actualLibPath) {
			utility.LogPrintWarning(fmt.Sprintf("库文件不存在，跳过: %s", actualLibPath))
			continue
		}

		// 首先修改库的install_name以确保路径正确
		if err := fixLibraryInstallName(actualLibPath, expectedRpathRef); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("修改库install_name失败: %v", err))
			continue
		}

		// 使用optool将库添加到主进程的Load Commands中
		if err := addLibraryToMainExecutable(optoolPath, info.MainExecutable, expectedRpathRef); err != nil {
			utility.LogPrintWarning(fmt.Sprintf("使用optool添加库到主进程失败: %v", err))
			continue
		}

		utility.LogPrintSuccess(fmt.Sprintf("成功修复未加载的库: %s", libPath))
	}

	return nil
}

// getLibraryInstallName 获取库的install_name
func getLibraryInstallName(libPath string) (string, error) {
	cmd := exec.Command("otool", "-D", libPath)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行otool -D命令失败: %w", err)
	}

	lines := strings.Split(string(output), "\n")
	// 第一行是文件路径，第二行是install_name
	if len(lines) >= 2 {
		installName := strings.TrimSpace(lines[1])
		if installName != "" {
			return installName, nil
		}
	}

	return "", fmt.Errorf("未找到install_name")
}

// ensureOptoolAvailable 确保optool工具可用，如果不存在则下载
func ensureOptoolAvailable() (string, error) {
	// 获取当前可执行文件的目录
	execPath, err := os.Executable()
	if err != nil {
		return "", fmt.Errorf("获取可执行文件路径失败: %w", err)
	}

	execDir := filepath.Dir(execPath)
	optoolPath := filepath.Join(execDir, "optool")

	// 检查optool是否已存在
	if utility.IsExist(optoolPath) {
		// 检查是否可执行
		if err := exec.Command(optoolPath, "--help").Run(); err == nil {
			utility.LogPrintInfo(fmt.Sprintf("找到optool工具: %s", optoolPath))
			return optoolPath, nil
		}
	}

	// 检查系统PATH中是否有optool
	if _, err := exec.LookPath("optool"); err == nil {
		utility.LogPrintInfo("使用系统PATH中的optool工具")
		return "optool", nil
	}

	// 下载optool
	utility.LogPrintInfo("optool工具不存在，正在从GitHub下载...")
	if err := downloadOptool(optoolPath); err != nil {
		return "", fmt.Errorf("下载optool失败: %w", err)
	}

	utility.LogPrintSuccess(fmt.Sprintf("optool工具下载完成: %s", optoolPath))
	return optoolPath, nil
}

// downloadOptool 从GitHub下载optool工具
func downloadOptool(targetPath string) error {
	downloadURL := "https://github.com/alexzielenski/optool/releases/download/0.1/optool.zip"

	utility.LogPrintInfo("正在下载optool.zip...")

	// 创建临时目录用于下载和解压
	tempDir, err := os.MkdirTemp("", "optool_download_*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}
	defer os.RemoveAll(tempDir)

	// 下载zip文件到临时目录
	zipPath := filepath.Join(tempDir, "optool.zip")
	cmd := exec.Command("curl", "-L", "-o", zipPath, downloadURL)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("下载optool.zip失败: %w", err)
	}

	utility.LogPrintInfo("正在解压optool.zip...")

	// 解压zip文件
	if err := extractOptoolFromZip(zipPath, tempDir); err != nil {
		return fmt.Errorf("解压optool.zip失败: %w", err)
	}

	// 查找解压后的optool可执行文件
	optoolSourcePath, err := findOptoolExecutable(tempDir)
	if err != nil {
		return fmt.Errorf("在解压文件中找不到optool可执行文件: %w", err)
	}

	// 复制optool到目标路径
	if err := copyFile(optoolSourcePath, targetPath); err != nil {
		return fmt.Errorf("复制optool到目标路径失败: %w", err)
	}

	// 设置可执行权限
	if err := os.Chmod(targetPath, 0755); err != nil {
		return fmt.Errorf("设置optool可执行权限失败: %w", err)
	}

	// 验证下载的文件是否可用
	if err := exec.Command(targetPath, "--help").Run(); err != nil {
		// 如果验证失败，删除下载的文件
		os.Remove(targetPath)
		return fmt.Errorf("下载的optool文件无法正常运行: %w", err)
	}

	return nil
}

// extractOptoolFromZip 解压optool.zip文件
func extractOptoolFromZip(zipPath, destDir string) error {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return fmt.Errorf("打开zip文件失败: %w", err)
	}
	defer reader.Close()

	for _, file := range reader.File {
		path := filepath.Join(destDir, file.Name)

		// 确保路径安全，防止zip slip攻击
		if !strings.HasPrefix(path, filepath.Clean(destDir)+string(os.PathSeparator)) {
			return fmt.Errorf("无效的文件路径: %s", file.Name)
		}

		if file.FileInfo().IsDir() {
			os.MkdirAll(path, file.FileInfo().Mode())
			continue
		}

		// 创建目录
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return fmt.Errorf("创建目录失败: %w", err)
		}

		// 解压文件
		fileReader, err := file.Open()
		if err != nil {
			return fmt.Errorf("打开zip文件内容失败: %w", err)
		}

		targetFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
		if err != nil {
			fileReader.Close()
			return fmt.Errorf("创建目标文件失败: %w", err)
		}

		_, err = io.Copy(targetFile, fileReader)
		fileReader.Close()
		targetFile.Close()

		if err != nil {
			return fmt.Errorf("复制文件内容失败: %w", err)
		}
	}

	return nil
}

// findOptoolExecutable 在解压目录中查找optool可执行文件
func findOptoolExecutable(dir string) (string, error) {
	var optoolPath string

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 查找名为optool的可执行文件
		if !info.IsDir() && info.Name() == "optool" && (info.Mode()&0111) != 0 {
			optoolPath = path
			return filepath.SkipDir // 找到后停止搜索
		}

		return nil
	})

	if err != nil {
		return "", err
	}

	if optoolPath == "" {
		return "", fmt.Errorf("在解压文件中未找到optool可执行文件")
	}

	return optoolPath, nil
}

// fixLibraryInstallName 修复库的install_name
func fixLibraryInstallName(libPath, expectedRpathRef string) error {
	// 获取当前install_name
	currentInstallName, err := getLibraryInstallName(libPath)
	if err != nil {
		return fmt.Errorf("获取库install_name失败: %w", err)
	}

	// 如果已经是期望的路径，则跳过
	if currentInstallName == expectedRpathRef {
		utility.LogPrintInfo(fmt.Sprintf("库install_name已正确: %s", expectedRpathRef))
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("修改库install_name: %s -> %s", currentInstallName, expectedRpathRef))

	// 使用install_name_tool修改install_name
	cmd := exec.Command("install_name_tool", "-id", expectedRpathRef, libPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("修改install_name失败: %w", err)
	}

	return nil
}

// addLibraryToMainExecutable 使用optool将库添加到主可执行文件的Load Commands中
func addLibraryToMainExecutable(optoolPath, mainExecPath, libraryPath string) error {
	// 首先检查库是否已经在Load Commands中
	if isLibraryAlreadyLoaded(mainExecPath, libraryPath) {
		utility.LogPrintInfo(fmt.Sprintf("库已在Load Commands中: %s", libraryPath))
		return nil
	}

	utility.LogPrintInfo(fmt.Sprintf("使用optool添加库到主进程: %s", libraryPath))

	// 使用optool的install命令添加库
	cmd := exec.Command(optoolPath, "install", "-c", "load", "-p", libraryPath, "-t", mainExecPath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("optool执行失败: %w, 输出: %s", err, string(output))
	}

	utility.LogPrintInfo(fmt.Sprintf("optool输出: %s", string(output)))

	// 验证库是否成功添加
	if !isLibraryAlreadyLoaded(mainExecPath, libraryPath) {
		return fmt.Errorf("库添加后验证失败，可能未成功添加到Load Commands")
	} else {
		utility.LogPrintWarning(fmt.Sprintf("动态注入库: %s，因ASLR安全问题，重签后的包只可用于测试，不能用于发布", libraryPath))
	}

	return nil
}

// isLibraryAlreadyLoaded 检查库是否已经在主可执行文件的Load Commands中
func isLibraryAlreadyLoaded(mainExecPath, libraryPath string) bool {
	cmd := exec.Command("otool", "-L", mainExecPath)
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, libraryPath) {
			return true
		}
	}

	return false
}
