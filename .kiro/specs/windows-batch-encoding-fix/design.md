# Design Document

## Overview

Windows批处理文件编码问题的根本原因是文件可能以UTF-8无BOM格式保存，而Windows命令提示符默认使用系统代码页（通常是GBK/CP936用于中文系统）。本设计提供多种解决方案来确保批处理文件的兼容性。

## Architecture

### 编码解决方案架构
```
用户执行 → 检测系统编码 → 选择合适的执行方法 → 成功安装
    ↓
批处理文件 (GBK编码) 
    ↓
PowerShell脚本 (UTF-8 with BOM)
    ↓  
英文版批处理文件 (ASCII)
```

## Components and Interfaces

### 1. 编码转换组件
- **功能**: 将现有的UTF-8批处理文件转换为GBK编码
- **输入**: install_windows.bat (UTF-8)
- **输出**: install_windows.bat (GBK编码)

### 2. PowerShell替代脚本
- **功能**: 提供与批处理文件相同功能的PowerShell版本
- **编码**: UTF-8 with BOM
- **兼容性**: Windows PowerShell 2.0+

### 3. 英文版批处理文件
- **功能**: 提供纯ASCII字符的批处理文件
- **优势**: 避免所有编码问题
- **缺点**: 用户体验不如中文版

### 4. 代码页设置组件
- **功能**: 在批处理文件开头设置正确的代码页
- **命令**: `chcp 65001` (UTF-8) 或 `chcp 936` (GBK)

## Data Models

### 编码检测模型
```
EncodingInfo {
    currentCodePage: string
    systemLocale: string
    recommendedEncoding: string
    supportedMethods: string[]
}
```

### 安装配置模型
```
InstallConfig {
    method: "batch" | "powershell" | "manual"
    encoding: "gbk" | "utf8" | "ascii"
    language: "zh-CN" | "en-US"
}
```

## Error Handling

### 编码错误处理
1. **检测编码问题**: 在脚本开始时检测当前代码页
2. **自动切换**: 如果检测到编码问题，自动切换到PowerShell版本
3. **用户提示**: 提供清晰的错误信息和解决方案

### 回退机制
1. 批处理文件失败 → PowerShell脚本
2. PowerShell脚本失败 → 手动安装指南
3. 所有方法失败 → 详细的故障排除文档

## Testing Strategy

### 编码测试
1. **多系统测试**: 在不同Windows版本上测试
2. **代码页测试**: 测试不同的系统代码页设置
3. **字符集测试**: 验证中文字符正确显示

### 兼容性测试
1. **Windows版本**: Windows 7, 8, 10, 11
2. **PowerShell版本**: 2.0, 3.0, 5.0, 7.0+
3. **系统语言**: 中文、英文、其他语言系统

### 功能测试
1. **安装流程**: 验证完整的安装过程
2. **错误恢复**: 测试各种错误场景的恢复
3. **用户体验**: 确保用户友好的错误信息

## Implementation Approach

### 阶段1: 立即修复
- 将批处理文件转换为GBK编码
- 在文件开头添加代码页设置

### 阶段2: 增强方案
- 创建PowerShell版本的安装脚本
- 添加自动编码检测和切换逻辑

### 阶段3: 完善体验
- 创建英文版本作为备选
- 添加详细的故障排除文档
- 实现智能安装方法选择