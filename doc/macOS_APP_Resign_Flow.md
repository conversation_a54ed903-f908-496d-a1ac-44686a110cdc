# macOS .app 重签名详细执行流程

## 完整流程图

```mermaid
flowchart TD
    Start([开始执行 onetools app resign]) --> ValidateParams{参数验证}
    
    %% 参数验证阶段
    ValidateParams -->|检查文件路径| CheckAppPath{APP文件是否存在?}
    CheckAppPath -->|否| Error1[❌ 错误终止<br/>文件不存在或非zip格式]
    CheckAppPath -->|是| CheckCertType{证书类型是否有效?<br/>为空时跳过检查}
    CheckCertType -->|否| Error2[❌ 错误终止<br/>证书类型无效<br/>支持dev,dis,did]
    CheckCertType -->|是| InitLogger[初始化日志管理器]
    
    %% 初始化阶段
    InitLogger --> CreateTempDir[创建临时目录结构<br/>resignTemp_timestamp]
    CreateTempDir --> CreateUnzipDir[创建解压目录<br/>unzipOriginApp]
    CreateUnzipDir --> CheckInputType{输入文件类型?}
    
    CheckInputType -->|.zip文件| CopyZip[复制ZIP到临时目录]
    CheckInputType -->|.app文件夹| CopyApp[直接拷贝.app到临时目录]
    
    CopyZip --> UnzipApp[解压ZIP文件<br/>使用UnzipIPAExe函数]
    CopyApp --> FindApp[查找.app文件<br/>获取应用名称和Contents路径]
    
    UnzipApp -->|解压失败| Error3[❌ 错误终止<br/>ZIP文件损坏或格式错误]
    UnzipApp -->|解压成功| FindApp
    
    %% 应用信息获取
    FindApp -->|未找到app| Error4[❌ 错误终止<br/>文件结构异常未找到app文件]
    FindApp -->|找到app| GetAppInfo[获取应用信息<br/>appName和appContentRootPath<br/>app/Contents路径]
    
    %% Info.plist 基础更新
    GetAppInfo --> UpdateBasicPlist[更新Info.plist基础信息<br/>路径app/Contents/Info.plist]
    UpdateBasicPlist --> UpdateBundleID{是否更新Bundle ID?}
    UpdateBundleID -->|是| SetBundleID[设置CFBundleIdentifier]
    UpdateBundleID -->|否| UpdateDisplayName{是否更新显示名称?}
    SetBundleID --> UpdateDisplayName
    UpdateDisplayName -->|是| SetDisplayName[设置CFBundleDisplayName<br/>CFBundleName]
    UpdateDisplayName -->|否| UpdateVersion{是否更新版本?}
    SetDisplayName --> UpdateVersion
    UpdateVersion -->|是| SetVersion[设置CFBundleShortVersionString<br/>CFBundleVersion]
    UpdateVersion -->|否| CheckDevConfig{是否有AppDevConfig?}
    SetVersion --> CheckDevConfig
    
    %% Dev后台配置处理
    CheckDevConfig -->|是| ParseDevConfig[解析AppDevConfig参数<br/>格式appid=xxx,env=xxx,host=xxx]
    CheckDevConfig -->|否| ProcessPlistMods[处理plist修改和合并]
    
    ParseDevConfig --> CreateDevDir[创建appDevConfig目录]
    CreateDevDir --> FetchDevConfig[调用FetchProjectConfig<br/>从dev后台获取配置]
    FetchDevConfig -->|获取失败| Error5[❌ 错误终止<br/>dev后台配置获取失败]
    FetchDevConfig -->|获取成功| HandleDevConfig[处理dev配置文件]
    
    %% Dev配置处理详细步骤 (macOS特殊处理)
    HandleDevConfig --> CopyDevFiles[拷贝Config目录文件到<br/>app/Contents/Resources<br/>忽略dynamic.json]
    CopyDevFiles --> ParseDynamicJSON[解析dynamic.json文件]
    ParseDynamicJSON --> GenerateDevPlist[生成plist配置<br/>处理urlTypes、capabilities]
    GenerateDevPlist --> GenerateDevEntitlements[生成entitlements配置<br/>处理signInWithApple、associatedDomains等<br/>注意:macOS不支持pushNotifications]
    GenerateDevEntitlements --> ProcessPlistMods
    
    %% plist修改和文件操作
    ProcessPlistMods --> RemovePlistKeys{是否删除plist keys?}
    RemovePlistKeys -->|是| DeleteKeys[删除指定的顶层key]
    RemovePlistKeys -->|否| MergeAdditionalPlist{是否合并附加plist?}
    DeleteKeys --> MergeAdditionalPlist
    MergeAdditionalPlist -->|是| MergePlist[合并附加plist文件到Info.plist]
    MergeAdditionalPlist -->|否| DeleteFiles{是否删除文件?}
    MergePlist --> DeleteFiles
    
    DeleteFiles -->|是| DeleteAppFiles[删除app/Contents目录中<br/>指定文件和文件夹]
    DeleteFiles -->|否| AddFiles{是否添加文件?}
    DeleteAppFiles --> AddFiles
    AddFiles -->|是| AddAppFiles[添加文件到app/Contents目录<br/>支持覆盖和新增]
    AddFiles -->|否| InstallProvision[安装provisionprofile文件]
    AddAppFiles --> InstallProvision
    
    %% provisionprofile处理
    InstallProvision --> CheckProvisionPath{provision路径是否指定?}
    CheckProvisionPath -->|否| AutoFindProvision[根据cert-type和bundle-id<br/>自动查找provisionprofile]
    CheckProvisionPath -->|是| ValidateProvision{provisionprofile文件是否存在?}
    AutoFindProvision -->|未找到| UseEmbedded[使用当前app中的<br/>embedded.provisionprofile]
    AutoFindProvision -->|找到| CopyProvision[复制provisionprofile到<br/>app/Contents目录]
    ValidateProvision -->|否| Error6[❌ 错误终止<br/>指定的provisionprofile文件不存在]
    ValidateProvision -->|是| CopyProvision
    UseEmbedded --> CopyProvision
    CopyProvision --> ReplaceIcon{是否替换应用图标?}
    
    %% 图标处理 (macOS特殊)
    ReplaceIcon -->|是| ProcessMacIcon[处理macOS应用图标<br/>转换为icns格式<br/>更新app/Contents/Resources]
    ReplaceIcon -->|否| CheckRpath[检查rpath配置]
    ProcessMacIcon -->|处理失败| Error7[❌ 错误终止<br/>图标文件格式错误或icns转换失败]
    ProcessMacIcon -->|处理成功| CheckRpath
    
    %% rpath检查和修复
    CheckRpath --> AnalyzeRpath[分析rpath信息<br/>扫描app/Contents/Frameworks<br/>中的动态库]
    AnalyzeRpath --> CheckUnloaded{是否有未加载的动态库?}
    CheckUnloaded -->|是| FixUnloaded[修复未加载的动态库<br/>添加到主程序Load Commands]
    CheckUnloaded -->|否| CheckMissingRpath{是否有缺失的rpath?}
    FixUnloaded --> CheckMissingRpath
    CheckMissingRpath -->|是| FixRpath[修复rpath配置<br/>添加缺失的LC_RPATH]
    CheckMissingRpath -->|否| GetCertificate[获取签名证书]
    FixRpath --> GetCertificate
    
    %% 证书获取 (macOS证书类型)
    GetCertificate --> CheckCertParam{是否指定证书?}
    CheckCertParam -->|是| UseCert[使用指定证书<br/>支持Developer ID类型]
    CheckCertParam -->|否| CheckProvisionCert{是否有provisionprofile文件?}
    UseCert --> ExtractEntitlements[提取和合并entitlements]
    CheckProvisionCert -->|是| MatchCert[根据provisionprofile匹配证书<br/>使用MatchCertificateFromProfile]
    CheckProvisionCert -->|否| GetCurrentCert[获取当前.app使用的证书]
    MatchCert -->|匹配失败| Error8[❌ 错误终止<br/>未找到匹配的证书]
    MatchCert -->|匹配成功| ExtractEntitlements
    GetCurrentCert -->|获取失败| Error9[❌ 错误终止<br/>无法获取当前应用证书]
    GetCurrentCert -->|获取成功| ExtractEntitlements
    
    %% entitlements处理 (macOS特殊权限)
    ExtractEntitlements --> CheckCustomEntitlements{是否有自定义entitlements?}
    CheckCustomEntitlements -->|是| UseCustom[使用自定义entitlements文件]
    CheckCustomEntitlements -->|否| ExtractFromBinary[从应用二进制提取entitlements<br/>路径app/Contents/MacOS/appName]
    UseCustom --> MergeDevEntitlements{是否有dev后台配置的entitlements?}
    ExtractFromBinary --> ExtractFromProvision[从provisionprofile提取entitlements]
    ExtractFromProvision --> MergeEntitlements[合并原应用和新provision的entitlements<br/>保留sandbox、network等权限]
    MergeEntitlements --> MergeDevEntitlements
    MergeDevEntitlements -->|是| MergeDevRights[合并dev后台配置的权限<br/>signInWithApple、associatedDomains<br/>注意:macOS不支持pushNotifications]
    MergeDevEntitlements -->|否| RemoveOldSignature[删除原有签名目录<br/>_CodeSignature]
    MergeDevRights --> RemoveOldSignature
    
    %% 代码签名阶段
    RemoveOldSignature --> ScanLibraries[扫描app/Contents/Frameworks<br/>中的动态库和框架]
    ScanLibraries --> SignLibraries[依次签名动态库<br/>使用codesign命令<br/>支持.framework和.dylib]
    SignLibraries -->|签名失败| Error10[❌ 错误终止<br/>动态库签名失败]
    SignLibraries -->|签名成功| SignMainApp[签名主应用<br/>使用entitlements文件<br/>签名整个app包]
    SignMainApp -->|签名失败| Error11[❌ 错误终止<br/>主应用签名失败]
    SignMainApp -->|签名成功| VerifySignature[验证签名<br/>使用codesign -dvvv]
    
    VerifySignature -->|验证失败| Error12[❌ 错误终止<br/>签名验证失败]
    VerifySignature -->|验证成功| CheckOutputFormat{输出格式?}
    
    %% 输出格式处理
    CheckOutputFormat -->|.app格式| CopyAppOutput[直接拷贝.app文件夹<br/>到输出路径]
    CheckOutputFormat -->|.zip格式| CreateZip[压缩为ZIP文件]
    
    CopyAppOutput -->|拷贝失败| Error13[❌ 错误终止<br/>.app文件拷贝失败]
    CopyAppOutput -->|拷贝成功| CheckKeepTemp{是否保留临时文件?}
    
    CreateZip --> GenerateZipOutput[生成输出路径<br/>默认appName_resigned_timestamp.zip]
    GenerateZipOutput --> ZipApp[使用zip命令压缩<br/>zip -r -q -y output.zip appName.app]
    ZipApp -->|压缩失败| Error14[❌ 错误终止<br/>ZIP文件创建失败]
    ZipApp -->|压缩成功| CheckKeepTemp
    
    %% 清理和完成
    CheckKeepTemp -->|是| KeepTemp[保留临时目录用于调试]
    CheckKeepTemp -->|否| CleanTemp[清理临时文件]
    KeepTemp --> Success[✅ 重签名成功<br/>输出文件路径和日志]
    CleanTemp --> Success
    
    %% 样式定义
    classDef errorClass fill:#ffebee,stroke:#f44336,stroke-width:2px,color:#000
    classDef successClass fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#000
    classDef processClass fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#000
    classDef decisionClass fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#000
    classDef macosClass fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px,color:#000
    
    class Error1,Error2,Error3,Error4,Error5,Error6,Error7,Error8,Error9,Error10,Error11,Error12,Error13,Error14 errorClass
    class Success successClass
    class ValidateParams,CheckAppPath,CheckCertType,CheckInputType,UpdateBundleID,UpdateDisplayName,UpdateVersion,CheckDevConfig,RemovePlistKeys,MergeAdditionalPlist,DeleteFiles,AddFiles,CheckProvisionPath,ValidateProvision,ReplaceIcon,CheckUnloaded,CheckMissingRpath,CheckCertParam,CheckProvisionCert,CheckCustomEntitlements,MergeDevEntitlements,CheckOutputFormat,CheckKeepTemp decisionClass
    class ProcessMacIcon,CopyDevFiles,GenerateDevEntitlements,DeleteAppFiles,AddAppFiles,CopyProvision,AnalyzeRpath,MergeDevRights,ScanLibraries,CopyAppOutput,CreateZip macosClass
```

## macOS平台特殊处理说明

### 1. 文件结构差异
**macOS .app结构:**
```
MyApp.app/
├── Contents/
│   ├── Info.plist                    # 应用信息文件
│   ├── MacOS/
│   │   └── MyApp                     # 主可执行文件
│   ├── Resources/                    # 资源文件目录
│   │   ├── AppIcon.icns             # 应用图标
│   │   └── config.json              # 配置文件
│   ├── Frameworks/                   # 动态库目录
│   │   ├── UnityFramework.framework
│   │   └── custom.dylib
│   └── embedded.provisionprofile     # 描述文件
```

**与iOS差异:**
- iOS: `Payload/App.app/` → macOS: `App.app/Contents/`
- iOS: `embedded.mobileprovision` → macOS: `embedded.provisionprofile`
- iOS: 多尺寸PNG图标 → macOS: 单个.icns图标文件

### 2. 证书类型差异
**macOS支持的证书类型:**
- `dev`: Mac Developer (开发证书)
- `dis`: Mac App Store (AppStore发布证书)  
- `did`: Developer ID Application (侧载发布证书)

**与iOS差异:**
- 不支持`adhoc`和`ent`类型
- Developer ID证书用于非AppStore分发

### 3. 权限处理差异
**macOS特有权限:**
```xml
<!-- 沙盒权限 -->
<key>com.apple.security.app-sandbox</key>
<true/>

<!-- 网络访问权限 -->
<key>com.apple.security.network.client</key>
<true/>
<key>com.apple.security.network.server</key>
<true/>

<!-- 文件访问权限 -->
<key>com.apple.security.files.user-selected.read-only</key>
<true/>
```

**不支持的iOS权限:**
- `aps-environment` (推送通知)
- `get-task-allow` (调试权限)
- iOS特有的后台模式权限

### 4. 图标处理差异
**macOS图标处理流程:**
1. 检查输入图标格式 (PNG/JPEG)
2. 转换为.icns格式 (包含多种尺寸)
3. 替换`Contents/Resources/AppIcon.icns`
4. 更新Info.plist中的图标引用

**支持的图标尺寸:**
- 16x16, 32x32, 64x64, 128x128, 256x256, 512x512, 1024x1024

### 5. Dev后台配置差异
**macOS配置文件路径:**
- 配置文件复制到: `.app/Contents/Resources/`
- 不是iOS的`.app/`根目录

**权限处理差异:**
- 不处理`pushNotifications`相关权限
- 不添加`UIBackgroundModes`配置
- 专注于`signInWithApple`和`associatedDomains`

## 关键步骤详细说明

### 1. 输入文件类型检查 (CheckInputType)
**支持的输入格式:**
- `.zip`文件: 包含.app的压缩包
- `.app`文件夹: 直接的应用包

**处理逻辑:**
```go
if strings.HasSuffix(ipaName, ".zip") {
    // 解压ZIP文件
    UnzipIPAExe(tempTargetIpaPath, config.OriginAppUnzipPath)
} else {
    // 直接拷贝.app文件夹
    copyDirRecursively(config.IPAPath, tempDir)
}
```

### 2. 应用路径获取 (GetAppInfo)
**macOS特殊处理:**
```go
if platform == utility.PlatformOS_OSX {
    appContentRootPath = filepath.Join(path, "Contents")
} else {
    appContentRootPath = path
}
```

**返回路径说明:**
- `appName`: 应用名称 (去掉.app后缀)
- `appContentRootPath`: `.app/Contents`路径

### 3. 图标处理 (ProcessMacIcon)
**macOS图标处理流程:**
```go
func replaceOSXAppIcon(appPath, iconPath, tempDir string) error {
    // 1. 检查输入图标格式
    // 2. 使用sips命令转换为.icns
    // 3. 替换Contents/Resources/中的图标文件
    // 4. 更新Info.plist中的CFBundleIconFile
}
```

### 4. 签名证书匹配
**macOS证书名称格式:**
- 开发证书: `Mac Developer: Your Name (XXXXXXXXXX)`
- AppStore证书: `3rd Party Mac Developer Application: Your Name (XXXXXXXXXX)`
- Developer ID证书: `Developer ID Application: Your Name (XXXXXXXXXX)`

### 5. 输出格式处理 (CheckOutputFormat)
**输出格式判断:**
```go
if strings.HasSuffix(outputPath, ".app") {
    // 直接拷贝.app文件夹
    copyDirRecursively(unArchiveAppFilePath, outputPath)
} else {
    // 压缩为ZIP文件
    zip -r -q -y output.zip MyApp.app
}
```

## 错误处理和日志示例

### 成功执行日志:
```
[INFO] 开始重签名APP文件: /path/to/MyApp.app.zip
[INFO] 清理并创建临时目录: /path/to/resignTemp_20250103_150405.000
[INFO] 复制并解压包文件...
[INFO] 可执行文件名称: MyApp
[INFO] 更新Bundle ID: com.example.newapp
[INFO] 根据类型，自动匹配到provisionprofile文件: Mac Developer Profile
[INFO] 成功安装provisionprofile文件
[INFO] 处理macOS应用图标，转换为.icns格式
[INFO] 成功替换应用图标: AppIcon.icns
[INFO] 检查Frameworks中动态库的rpath配置...
[INFO] rpath配置检查通过，无需修复
[INFO] 使用指定的证书: Mac Developer: Your Name (XXXXXXXXXX)
[INFO] 成功提取和合并entitlements
[INFO] 删除原有签名目录: _CodeSignature
[INFO] 签名动态库: UnityFramework.framework
[INFO] 成功签名动态库: UnityFramework.framework
[INFO] 签名主应用: MyApp.app
[INFO] 成功签名主应用
[INFO] 签名验证通过
[INFO] 使用zip命令创建ZIP文件: /path/to/MyApp_resigned_20250103_150405.zip
[INFO] 开始压缩文件(MyApp.app)
[INFO] 压缩前总文件大小: 89.34 MB
[SUCCESS] ZIP文件压缩完成: /path/to/MyApp_resigned_20250103_150405.zip (大小: 45.67 MB)
[INFO] 清理临时目录: /path/to/resignTemp_20250103_150405.000
[SUCCESS] APP重签名完成,路径: /path/to/MyApp_resigned_20250103_150405.zip
```

### 常见错误示例:
```
[ERROR] 错误：【/path/to/app.ipa】路径不存在，请确认app.zip路径是否正确，只接受.zip文件
[ERROR] 错误：不支持的证书类型 'adhoc'，支持的类型：dev(开发)、dis(提交到AppStore)、did(侧载发布)
[ERROR] 图标文件格式错误或.icns转换失败: 不支持的图片格式
[ERROR] 未找到匹配Bundle ID 'com.example.app' 和类型 'dis' 的配置文件
[ERROR] 主应用签名失败: errSecInternalComponent
```

## 平台对比总结

| 特性 | iOS (.ipa) | macOS (.app/.zip) |
|------|------------|-------------------|
| **输入格式** | .ipa文件 | .zip文件或.app文件夹 |
| **应用结构** | Payload/.app/ | .app/Contents/ |
| **描述文件** | embedded.mobileprovision | embedded.provisionprofile |
| **证书类型** | dev/adhoc/dis/ent | dev/dis/did |
| **图标格式** | 多尺寸PNG | 单个.icns文件 |
| **启动图** | 支持LaunchScreen | 不支持 |
| **沙盒权限** | 基本权限 | 详细沙盒权限 |
| **推送通知** | 支持aps-environment | 不支持 |
| **SwiftSupport** | 需要处理 | 不需要 |
| **输出格式** | .ipa文件 | .app文件夹或.zip文件 |
| **签名验证** | iOS证书链 | macOS证书链 |