# iOS IPA 重签名详细执行流程

## 完整流程图

```mermaid

flowchart TD
    Start([开始执行 onetools ipa resign]) --> ValidateParams{参数验证}
    
    %% 1.参数验证阶段
    ValidateParams -->|检查文件路径| CheckIPAPath{IPA文件是否存在?}
    CheckIPAPath -->|否| Error1[❌ 错误终止<br/>文件不存在或非.ipa格式]
    CheckIPAPath -->|是| CheckCertType{证书类型是否有效?<br/>为空时跳过检查}
    CheckCertType -->|否| Error2[❌ 错误终止<br/>证书类型无效<br/>支持: dev/adhoc/dis/ent]
    CheckCertType -->|是| InitLogger[初始化日志管理器]
    
    %% 2.初始化阶段
    InitLogger --> CreateTempDir[创建临时目录结构<br/>resignTemp_timestamp/]
    CreateTempDir --> CreateUnzipDir[创建解压目录<br/>unzipOriginApp/]
    CreateUnzipDir --> CopyIPA[复制IPA到临时目录]
    CopyIPA --> UnzipIPA[解压IPA文件<br/>使用UnzipIPAExe函数]
    
    UnzipIPA -->|解压失败| Error3[❌ 错误终止<br/>IPA文件损坏或格式错误]
    UnzipIPA -->|解压成功| FindApp[查找Payload/.app文件<br/>获取应用名称和路径]
    
    %% 3.应用信息获取
    FindApp -->|未找到.app| Error4[❌ 错误终止<br/>IPA结构异常，未找到.app文件]
    FindApp -->|找到.app| GetAppInfo[获取应用信息<br/>appName, appContentRootPath]
    
    %% 4.Info.plist 基础更新
    GetAppInfo --> UpdateBasicPlist[更新Info.plist基础信息]
    UpdateBasicPlist --> UpdateBundleID{是否更新Bundle ID?}
    UpdateBundleID -->|是| SetBundleID[设置CFBundleIdentifier]
    UpdateBundleID -->|否| UpdateDisplayName{是否更新显示名称?}
    SetBundleID --> UpdateDisplayName
    UpdateDisplayName -->|是| SetDisplayName[设置CFBundleDisplayName<br/>CFBundleName]
    UpdateDisplayName -->|否| UpdateVersion{是否更新版本?}
    SetDisplayName --> UpdateVersion
    UpdateVersion -->|是| SetVersion[设置CFBundleShortVersionString<br/>CFBundleVersion]
    UpdateVersion -->|否| CheckDevConfig{是否有AppDevConfig?}
    SetVersion --> CheckDevConfig
    
    %% 5.Dev后台配置处理
    CheckDevConfig -->|是| ParseDevConfig[解析AppDevConfig参数<br/>格式: appid=xxx,env=xxx,host=xxx]
    CheckDevConfig -->|否| ProcessPlistMods[处理plist修改和合并]
    
    ParseDevConfig --> CreateDevDir[创建appDevConfig目录]
    CreateDevDir --> FetchDevConfig[调用FetchProjectConfig<br/>从dev后台获取配置]
    FetchDevConfig -->|获取失败| Error5[❌ 错误终止<br/>dev后台配置获取失败]
    FetchDevConfig -->|获取成功| HandleDevConfig[处理dev配置文件]
    
    %% Dev配置处理详细步骤
    HandleDevConfig --> CopyDevFiles[拷贝Config目录文件<br/>忽略dynamic.json]
    CopyDevFiles --> ParseDynamicJSON[解析dynamic.json文件]
    ParseDynamicJSON --> GenerateDevPlist[生成plist配置<br/>处理urlTypes、capabilities]
    GenerateDevPlist --> GenerateDevEntitlements[生成entitlements配置<br/>处理signInWithApple、pushNotifications等]
    GenerateDevEntitlements --> ProcessPlistMods
    
    %% 6.plist修改和文件操作
    ProcessPlistMods --> RemovePlistKeys{是否删除plist keys?}
    RemovePlistKeys -->|是| DeleteKeys[删除指定的顶层key]
    RemovePlistKeys -->|否| MergeAdditionalPlist{是否合并附加plist?}
    DeleteKeys --> MergeAdditionalPlist
    MergeAdditionalPlist -->|是| MergePlist[合并附加plist文件到Info.plist]
    MergeAdditionalPlist -->|否| DeleteFiles{是否删除文件?}
    MergePlist --> DeleteFiles
    
    DeleteFiles -->|是| DeleteAppFiles[删除.app目录中指定文件/文件夹]
    DeleteFiles -->|否| AddFiles{是否添加文件?}
    DeleteAppFiles --> AddFiles
    AddFiles -->|是| AddAppFiles[添加文件到.app目录<br/>支持覆盖和新增]
    AddFiles -->|否| InstallProvision[安装mobileprovision文件]
    AddAppFiles --> InstallProvision
    
    %% 7.mobileprovision处理
    InstallProvision --> CheckProvisionPath{provision路径是否指定?}
    CheckProvisionPath -->|否| AutoFindProvision[根据cert-type和bundle-id<br/>自动查找provision]
    CheckProvisionPath -->|是| ValidateProvision{provision文件是否存在?}
    AutoFindProvision -->|未找到| UseEmbedded[使用当前IPA中的<br/>embedded.mobileprovision]
    AutoFindProvision -->|找到| CopyProvision[复制provision到.app目录]
    ValidateProvision -->|否| Error6[❌ 错误终止<br/>指定的provision文件不存在]
    ValidateProvision -->|是| CopyProvision
    UseEmbedded --> CopyProvision
    CopyProvision --> ReplaceIcon{是否替换应用图标?}
    
    %% 8.图标和启动图处理
    ReplaceIcon -->|是| ProcessIcon[处理应用图标<br/>支持1024x1024 PNG或Assets.xcassets]
    ReplaceIcon -->|否| ReplaceLaunch{是否替换启动图?}
    ProcessIcon -->|处理失败| Error7[❌ 错误终止<br/>图标文件格式错误或处理失败]
    ProcessIcon -->|处理成功| ReplaceLaunch
    ReplaceLaunch -->|是| ProcessLaunch[处理启动图<br/>替换LaunchScreen相关资源]
    ReplaceLaunch -->|否| CheckRpath[检查rpath配置]
    ProcessLaunch --> CheckRpath
    
    %% 9.rpath检查和修复
    CheckRpath --> AnalyzeRpath[分析rpath信息<br/>扫描Frameworks中的动态库]
    AnalyzeRpath --> CheckUnloaded{是否有未加载的动态库?}
    CheckUnloaded -->|是| FixUnloaded[修复未加载的动态库<br/>添加到主程序Load Commands]
    CheckUnloaded -->|否| CheckMissingRpath{是否有缺失的rpath?}
    FixUnloaded --> CheckMissingRpath
    CheckMissingRpath -->|是| FixRpath[修复rpath配置<br/>添加缺失的LC_RPATH]
    CheckMissingRpath -->|否| GetCertificate[获取签名证书]
    FixRpath --> GetCertificate
    
    %% 10.证书获取
    GetCertificate --> CheckCertParam{是否指定证书?}
    CheckCertParam -->|是| UseCert[使用指定证书]
    CheckCertParam -->|否| CheckProvisionCert{是否有provision文件?}
    UseCert --> ExtractEntitlements[提取和合并entitlements]
    CheckProvisionCert -->|是| MatchCert[根据provision匹配证书<br/>使用MatchCertificateFromProfile]
    CheckProvisionCert -->|否| GetCurrentCert[获取当前.app使用的证书]
    MatchCert -->|匹配失败| Error8[❌ 错误终止<br/>未找到匹配的证书]
    MatchCert -->|匹配成功| ExtractEntitlements
    GetCurrentCert -->|获取失败| Error9[❌ 错误终止<br/>无法获取当前应用证书]
    GetCurrentCert -->|获取成功| ExtractEntitlements
    
    %% 11.entitlements处理
    ExtractEntitlements --> CheckCustomEntitlements{是否有自定义entitlements?}
    CheckCustomEntitlements -->|是| UseCustom[使用自定义entitlements文件]
    CheckCustomEntitlements -->|否| ExtractFromBinary[从应用二进制提取entitlements]
    UseCustom --> MergeDevEntitlements{是否有dev后台配置的entitlements?}
    ExtractFromBinary --> ExtractFromProvision[从provision提取entitlements]
    ExtractFromProvision --> MergeEntitlements[合并原应用和新provision的entitlements<br/>保留associated-domains等权限]
    MergeEntitlements --> MergeDevEntitlements
    MergeDevEntitlements -->|是| MergeDevRights[合并dev后台配置的权限<br/>signInWithApple、pushNotifications等]
    MergeDevEntitlements -->|否| RemoveOldSignature[删除原有签名目录<br/>_CodeSignature]
    MergeDevRights --> RemoveOldSignature
    
    %% 12.代码签名阶段
    RemoveOldSignature --> ScanLibraries[扫描Frameworks中的动态库<br/>获取.framework和.dylib文件列表]
    ScanLibraries --> SignLibraries[依次签名动态库<br/>使用codesign命令]
    SignLibraries -->|签名失败| Error10[❌ 错误终止<br/>动态库签名失败]
    SignLibraries -->|签名成功| SignMainApp[签名主应用<br/>使用entitlements文件]
    SignMainApp -->|签名失败| Error11[❌ 错误终止<br/>主应用签名失败]
    SignMainApp -->|签名成功| VerifySignature[验证签名<br/>使用codesign -dvvv]
    
    VerifySignature -->|验证失败| Error12[❌ 错误终止<br/>签名验证失败]
    VerifySignature -->|验证成功| CheckSwiftSupport{是否需要处理SwiftSupport?}
    
    %% 13.SwiftSupport处理
    CheckSwiftSupport -->|是| ProcessSwift[处理SwiftSupport<br/>更新Swift库文件]
    CheckSwiftSupport -->|否| CreateIPA[创建重签名IPA]
    ProcessSwift -->|处理失败| Error13[❌ 错误终止<br/>SwiftSupport处理失败]
    ProcessSwift -->|处理成功| CreateIPA
    
    %% 14.最终打包
    CreateIPA --> GenerateOutput[生成输出路径<br/>默认: appName_resigned_timestamp.ipa]
    GenerateOutput --> ZipIPA[使用zip命令压缩<br/>zip -r -q -y output.ipa Payload SwiftSupport]
    ZipIPA -->|压缩失败| Error14[❌ 错误终止<br/>IPA文件创建失败]
    ZipIPA -->|压缩成功| CheckKeepTemp{是否保留临时文件?}
    
    %% 15.清理和完成
    CheckKeepTemp -->|是| KeepTemp[保留临时目录用于调试]
    CheckKeepTemp -->|否| CleanTemp[清理临时文件]
    KeepTemp --> Success[✅ 重签名成功<br/>输出文件路径和日志]
    CleanTemp --> Success
    
    %% 样式定义
    classDef errorClass fill:#ffebee,stroke:#f44336,stroke-width:2px,color:#000
    classDef successClass fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#000
    classDef processClass fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#000
    classDef decisionClass fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#000
    
    class Error1,Error2,Error3,Error4,Error5,Error6,Error7,Error8,Error9,Error10,Error11,Error12,Error13,Error14 errorClass
    class Success successClass
    class ValidateParams,CheckIPAPath,CheckCertType,UpdateBundleID,UpdateDisplayName,UpdateVersion,CheckDevConfig,RemovePlistKeys,MergeAdditionalPlist,DeleteFiles,AddFiles,CheckProvisionPath,ValidateProvision,ReplaceIcon,ReplaceLaunch,CheckUnloaded,CheckMissingRpath,CheckCertParam,CheckProvisionCert,CheckCustomEntitlements,MergeDevEntitlements,CheckSwiftSupport,CheckKeepTemp decisionClass

```

## 关键步骤详细说明

### 1. 参数验证阶段 (ValidateParams)
**执行内容:**
- 检查IPA文件路径是否存在且以.ipa结尾
- 验证证书类型参数 (dev/adhoc/dis/ent)
- 验证其他可选参数格式

**可能失败原因:**
- 文件路径不存在或格式错误
- 证书类型不在支持范围内
- 必要参数缺失

### 2. 初始化阶段 (InitLogger → UnzipIPA)
**执行内容:**
- 根据LogOutputMode初始化日志管理器 (console/file)
- 创建临时目录: `resignTemp_20250103_150405.000/`
- 创建解压目录: `unzipOriginApp/`
- 复制IPA文件到临时目录
- 使用`UnzipIPAExe`函数解压IPA文件

**可能失败原因:**
- 临时目录创建权限不足
- IPA文件损坏或加密
- 磁盘空间不足

### 3. 应用信息获取 (FindApp → GetAppInfo)
**执行内容:**
- 在`Payload/`目录下查找`.app`文件
- 调用`getAppFileNameAndContentRootPath`函数
- 获取应用名称和内容根路径

**可能失败原因:**
- IPA结构异常，缺少Payload目录
- 未找到.app文件
- .app文件结构损坏

### 4. Info.plist基础更新 (UpdateBasicPlist)
**执行内容:**
- 读取并解析`Info.plist`文件
- 更新`CFBundleIdentifier` (Bundle ID)
- 更新`CFBundleDisplayName`和`CFBundleName` (显示名称)
- 更新`CFBundleShortVersionString` (应用版本)
- 更新`CFBundleVersion` (构建版本)

**可能失败原因:**
- Info.plist文件格式错误
- plist解析失败
- 文件写入权限问题

### 5. Dev后台配置处理 (CheckDevConfig → HandleDevConfig)
**执行内容:**
- 解析AppDevConfig参数: `appid=1000000,env=prod,host=xxx`
- 调用`FetchProjectConfig`从dev后台获取配置
- 处理下载的配置文件:
  - 拷贝Config目录文件 (忽略dynamic.json)
  - 解析dynamic.json生成plist配置
  - 处理urlTypes转换为CFBundleURLTypes
  - 处理capabilities生成entitlements权限

**可能失败原因:**
- dev后台服务不可用
- 参数格式错误
- 网络连接问题
- 配置文件格式错误

### 6. plist修改和文件操作 (ProcessPlistMods → AddAppFiles)
**执行内容:**
- 删除指定的plist顶层key
- 合并附加plist文件到Info.plist
- 删除.app目录中指定的文件/文件夹
- 添加新文件到.app目录 (支持覆盖和新增)

**可能失败原因:**
- plist合并冲突
- 文件删除权限不足
- 源文件不存在
- 目标路径冲突

### 7. mobileprovision处理 (InstallProvision)
**执行内容:**
- 检查是否指定provision路径
- 自动查找: 根据cert-type和bundle-id匹配
- 文件名查找: 在系统provision目录中搜索
- 复制provision文件为`embedded.mobileprovision`

**可能失败原因:**
- 指定的provision文件不存在
- 自动匹配未找到合适的provision
- Bundle ID不匹配
- provision文件已过期

### 8. 图标和启动图处理 (ReplaceIcon → ProcessLaunch)
**执行内容:**
- 处理应用图标:
  - 支持1024x1024 PNG图片
  - 支持Assets.xcassets文件
  - 生成多种尺寸的图标
- 处理启动图:
  - 替换LaunchScreen相关资源
  - 更新LaunchScreen.storyboard

**可能失败原因:**
- 图标文件格式不正确
- 图标尺寸不符合要求
- Assets.xcassets结构错误
- 启动图处理失败

### 9. rpath检查和修复 (CheckRpath → FixRpath)
**执行内容:**
- 扫描Frameworks目录中的动态库
- 分析主程序的LC_RPATH配置
- 检查未被加载的动态库
- 修复缺失的rpath配置
- 添加动态库到主程序Load Commands

**可能失败原因:**
- 动态库依赖关系复杂
- rpath配置冲突
- 主程序修改失败
- 动态库路径错误

### 10. 证书获取 (GetCertificate)
**执行内容:**
- 优先级: 指定证书 > provision匹配 > 当前应用证书
- 调用`MatchCertificateFromProfile`匹配证书
- 使用`getCurrentAppCertificate`获取当前证书
- 验证证书有效性

**可能失败原因:**
- 指定证书不存在
- provision中的证书未安装
- 证书已过期
- 钥匙串访问权限问题

### 11. entitlements处理 (ExtractEntitlements → MergeDevRights)
**执行内容:**
- 从应用二进制提取原有entitlements
- 从provision文件提取新entitlements
- 合并权限 (保留associated-domains等)
- 合并dev后台配置的权限:
  - signInWithApple: `com.apple.developer.applesignin`
  - pushNotifications: `aps-environment`
  - associatedDomains: `com.apple.developer.associated-domains`

**可能失败原因:**
- entitlements格式错误
- 权限冲突
- provision权限不足
- XML解析失败

### 12. 代码签名阶段 (RemoveOldSignature → VerifySignature)
**执行内容:**
- 删除原有`_CodeSignature`目录
- 扫描并签名Frameworks中的动态库:
  - `.framework`文件
  - `.dylib`文件
- 签名主应用包 (使用entitlements)
- 验证签名有效性

**可能失败原因:**
- 证书权限不足
- 动态库签名失败
- entitlements权限冲突
- 签名验证失败

### 13. SwiftSupport处理 (CheckSwiftSupport → ProcessSwift)
**执行内容:**
- 检查是否存在SwiftSupport目录
- 检查ForceSwiftUpdate参数
- 更新Swift库文件到最新版本
- 处理Swift库依赖关系

**可能失败原因:**
- Swift库版本不兼容
- Swift库文件损坏
- 依赖关系错误
- Xcode工具链问题

### 14. 最终打包 (CreateIPA → Success)
**执行内容:**
- 生成输出文件路径 (默认添加_resigned_timestamp后缀)
- 使用zip命令压缩: `zip -r -q -y output.ipa Payload SwiftSupport`
- 统计文件大小和压缩比
- 清理临时文件 (可选保留用于调试)

**可能失败原因:**
- 磁盘空间不足
- 输出路径权限问题
- zip命令执行失败
- 文件锁定问题

## 日志输出示例

```
[INFO] 开始重签名IPA文件: /path/to/app.ipa
[INFO] 清理并创建临时目录: /path/to/resignTemp_20250103_150405.000
[INFO] 复制并解压包文件...
[INFO] 可执行文件名称: MyApp
[INFO] 更新Bundle ID: com.example.newapp
[INFO] 更新显示名称: New App Name
[INFO] 根据类型，自动匹配到mobileprovision文件: Development Profile
[INFO] 成功安装mobileprovision文件
[INFO] 检查Frameworks中动态库的rpath配置...
[INFO] rpath配置检查通过，无需修复
[INFO] 使用指定的证书: iPhone Developer: Your Name (XXXXXXXXXX)
[INFO] 成功提取和合并entitlements
[INFO] 删除原有签名目录: _CodeSignature
[INFO] 签名动态库: UnityFramework.framework
[INFO] 成功签名动态库: UnityFramework.framework
[INFO] 签名主应用: MyApp
[INFO] 成功签名主应用
[INFO] 签名验证通过
[INFO] 跳过SwiftSupport处理
[INFO] 创建重签名IPA: /path/to/MyApp_resigned_20250103_150405.ipa
[INFO] 开始压缩文件(Payload SwiftSupport)
[INFO] 压缩前总文件大小: 156.78 MB
[INFO] 正在压缩，时间可能较长，请等待...
[SUCCESS] IPA文件压缩完成: /path/to/MyApp_resigned_20250103_150405.ipa (大小: 89.45 MB)
[INFO] 清理临时目录: /path/to/resignTemp_20250103_150405.000
[SUCCESS] IPA重签名完成,路径: /path/to/MyApp_resigned_20250103_150405.ipa
```