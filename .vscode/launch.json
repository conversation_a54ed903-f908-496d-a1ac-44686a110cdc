{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "show rpath",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "ipa",
                "build",
                "--path", 
                "/Users/<USER>/Desktop/辅助工具/BuildIPAProject/OneSDK_LH_Test",
                "--cert-type",
                "dev",
                "--app-dev-config",
                "appid=1001,env=develop",
                "--add-files-path",
                "/Users/<USER>/Desktop/辅助工具/BuildIPAProject/addFiles"
                ]
        },
        {
            "name": "OneSDK Install Config and Lib",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                 "-p", "/Users/<USER>/Desktop/UnrealDyLibTest", 
                 "--os",
                "mac",
                ]
        },
        {
            "name": "GlobalSDK Install Config and Lib",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install", 
                "-p", "/Users/<USER>/Desktop/pwrdwork/OneSDKUE4",
                 "-c", "appid=1000000,env=develop",
                 "-u", "http://dev-test.sys.wanmei.net"
                ]
        },
        {
            "name": "OneSDK Install With Set SDK Dir Path",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-p",
                "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE41", 
                "--os",
                "mac",
                "-d",
                "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE41/Engine/Plugins/zhuxianplugins/SDK",
                "-c", "appid=1001,env=develop,sdkVersion=6.2.1.0",
                ]
        },
        {
            "name": "Install Config",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                 "-p", "/Users/<USER>/Desktop/pwrdwork/OneSDK/OneSDKUE4_union", 
                 "-t", "config", 
                 "-c", "appid=1001,env=0" ]
        },
        {
            "name": "PS Install Config",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                 "-p", "/Users/<USER>/Desktop/GameConfigTest_union", 
                 "--os",
                "ps",
                "-c", 
                "env=0"
                ]
        },
        {
            "name": "Download SDK",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-p",
                "E:\\ue\\WMAllSDKDemoUE4",
                "-m",
                "WMGNSDK",
                "-g",
                "${workspaceFolder}\\Gradle"
            ]
        },
        {
            "name": "Download SDK For None Engine",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-t",
                "config",
                "-c",
                "env=dis,appid=10001,appkey=xxxxx,sdkregion=mainland,sdkversion=6.0.0.0",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/WMChatSDK_Unity/ChatSDKUnityDemo",
                "-d",
                "/Users/<USER>/Desktop/WMSDK/unity/WMChatSDK_Unity/ChatSDKUnityDemo/WPSDKConfig/iOS",
                "-l",
                "import_sdk_OneSDK_LH_log.log"
            ]
        },
        {
            "name": "Download Config For None Engine",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-t",
                "config",
                "-c",
                "env=0,appid=1001,sdkregion=mainland,sdkversion=6.0.0.0",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-d",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo/WPSDKConfig/Mac",
                "--mac",
                "-u",
                "http://dev-test.sys.wanmei.net/"
            ]
        },
        {
            "name": "Check plugin version for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "--list",
                "-s"
            ]
        },
        {
            "name": "Remove import plugin for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-u",
                "-s"
            ]
        },
        {
            "name": "Install import plugin for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-i"
            ]
        },
        {
            "name": "Install import plugin of specifying version for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-i",
                "-v",
                "1.0.1.7",
                "-s"
            ]
        },
        {
            "name": "Remove export plugin for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-u",
                "-e",
                "-s"
            ]
        },
        {
            "name": "Install export plugin for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-i",
                "-e",
                "-s"
            ]
        },
        {
            "name": "Install export the plugin of specifying version for U3D",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "plugin",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo",
                "-i",
                "-v",
                "2.0.14.0",
                "-e",
                "-s"
            ]
        },
        {
            "name": "Modify UBT",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "ubt", 
                "-e", "/Users/<USER>/Epic Games/UE_5.3/", 
                "-f", "-i", "-m"
            ]
        },
        {
            "name": "Scan Symbol",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "symbol",
                 "-p", "/Users/<USER>/Epic Games/UE_4.27/Engine/Intermediate/UnzippedFrameworks", 
                 "-t", "systemDeviceTypeFormatted"
                ]
        },
        {
            "name": "Symbolicate Crash Log",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "crash",
                "-i","/Users/<USER>/Desktop/crash/Crash一2022-07-11_13-53-34.crash",
                "-d","/Users/<USER>/Desktop/crash/Unity-iPhone.app.dSYM"
            ]
        },
        {
            "name": "Crash Log UUID",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "crash",
                "-u","/Users/<USER>/Desktop/crash"
            ]
        },
        {
            "name": "Localization Config For Xcode Project",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "xcode",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/iOSSDKImport-Unity/iOSSDKImportDemo/Build/iOS/Unity-iPhone.xcodeproj"
            ]
        },
        {
            "name": "onetools upgrade",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "upgrade"
            ]
        },
        {
            "name": "download ps config",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-t",
                "config",
                "--os",
                "ps",
                "-c",
                "appid=1001,env=ps_6.0.0.0",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo"
            ]
        },
        {
            "name": "download ios config",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-t",
                "config",
                "-c",
                "appid=1001,env=0,sdkversion=6.2.0.0",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo"
            ]
        },
        {
            "name": "update sdk",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "install",
                "-U",
                "--os",
                "mac",
                "-p",
                "/Users/<USER>/Desktop/WMSDK/unity/OneSDKUnity/OneSDKUnityDemo"
            ]
        },
        {
            "name": "ipa unzip",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "ipa",
                "unzip",
                "-p",
                "/Users/<USER>/Downloads/Client_20250401_182430.ipa",
                "-o",
                "/Users/<USER>/Downloads/2222",
            ]
        },
        {
            "name": "ipa fix swiftSupport",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "ipa",
                "swiftSupport",
                "-p",
                "/Users/<USER>/Downloads/Main_0_2038_0_Dev_378498_Build4461.ipa",
                "-f"
            ]
        },
        {
            "name": "unity help",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "unity",
                "--help"
            ]
        },
        {
            "name": "unity list",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "unity",
                "list",
                "-e",
                "debug"
            ]
        },
        {
            "name": "unity versions",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "unity",
                "versions",
                "-n",
                "SDKImportTool",
                "-e",
                "debug"
            ]
        },
        {
            "name": "unity install",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "unity",
                "install",
                "-n",
                "SDKImportTool",
                "-v",
                "2.0.7.0",
                "-p",
                "/Users/<USER>/Desktop/test2025",
                "-e",
                "debug"
            ]
        },
        {
            "name": "unity uninstall",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "unity",
                "uninstall",
                "-n",
                "SDKImportTool",
                "-v",
                "2.0.7.0-SNAPSHOT",
                "-p",
                "/Users/<USER>/Desktop/test2025"
            ]
        },
        {
            "name": "unity generatebuild",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "unity",
                "generatebuild",
                "-n",
                "Jackson20250609",
                "-m",
                "/Users/<USER>/Downloads/company/ios/PWGlobalSDK/Cer/compwrdglobalsdkdemonew.mobileprovision",
                "-p",
                "/Users/<USER>/Desktop/test2025"
            ]
        },
        {
                "name": "unity listinstalled",
                "type": "go",
                "request": "launch",
                "mode": "auto",
                "program": "${workspaceFolder}",
                "args": [
                    "unity",
                    "listinstalled",
                    "-p",
                    "/Users/<USER>/Desktop/test2025"
                ]
            }
    ]
}