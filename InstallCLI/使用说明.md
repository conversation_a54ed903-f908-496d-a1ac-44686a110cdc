# onetools 自动安装脚本使用说明

## 概述

本目录提供了 onetools 工具在 macOS 和 Windows 平台上的自动安装脚本，能够自动检测版本、下载、安装和升级 onetools 命令行工具。

## 文件说明

- `install_macos.sh` - macOS 自动安装脚本
- `install_windows.bat` - Windows 批处理安装脚本
- `install_windows.ps1` - Windows PowerShell 安装脚本 (功能丰富)
- `使用说明.md` - 使用说明文档

## 快速开始

### macOS 用户

#### 在线安装 (推荐)
```bash
curl -fsSL https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_macos.sh | bash
```

#### 本地安装
```bash
# 1. 下载脚本到本地
curl -fsSL -o install_macos.sh https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_macos.sh

# 2. 添加执行权限
chmod +x install_macos.sh

# 3. 运行安装
./install_macos.sh
```

### Windows 用户

#### 方式一：批处理脚本安装 (推荐，兼容性最好)

**在线安装**
```cmd
# 使用 curl (Windows 10 1803+ 内置)
curl -fsSL -o install_windows.bat https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_windows.bat && install_windows.bat

# 或使用 PowerShell 下载后运行
powershell -Command "Invoke-WebRequest -Uri 'https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_windows.bat' -OutFile 'install_windows.bat'" && install_windows.bat
```

**本地安装**
```cmd
# 1. 下载 install_windows.bat 文件到本地
# 2. 双击运行，或在命令提示符中运行：
install_windows.bat
```

**特点**：
- ✅ 兼容所有 Windows 版本 (Windows 7+)
- ✅ 无需 PowerShell 或特殊权限
- ✅ 自动检测系统工具 (curl/PowerShell/tar)
- ✅ 完整的错误处理和版本管理

#### 方式二：PowerShell 脚本安装

**在线安装install_windows.bat**
```powershell
Invoke-WebRequest -Uri 'https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_windows.bat' -OutFile "$env:TEMP\install_windows.bat"; & "$env:TEMP\install_windows.bat"
```

**在线安装install_windows.ps1**
```powershell
iex ((New-Object System.Net.WebClient).DownloadString('https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_windows.ps1'))
```

**本地安装**
```powershell
# 1. 下载脚本
Invoke-WebRequest -Uri "https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/install_windows.ps1" -OutFile "install_windows.ps1"

# 2. 运行安装
powershell -ExecutionPolicy Bypass -File "install_windows.ps1"
```

**特点**：
- ✅ 功能丰富，错误处理完善
- ❌ 需要 PowerShell 3.0+ 和 .NET Framework 4.5+
- ❌ 可能受执行策略限制

## 安装位置

### macOS
- **安装目录**: `~/onetools/` (用户目录)
- **可执行文件**: `~/onetools/onetools`
- **Gradle 目录**: `~/onetools/Gradle/`

### Windows
- **安装目录**: `%USERPROFILE%\onetools\` (用户目录)
- **可执行文件**: `%USERPROFILE%\onetools\onetools.exe`
- **Gradle 目录**: `%USERPROFILE%\onetools\Gradle\`

脚本会自动将安装目录添加到系统 PATH 环境变量中。
> cmd查看环境变量：echo %PATH% 
> powerShell查看环境变量：[Environment]::GetEnvironmentVariable("PATH", "User")

## 卸载方法

### macOS
```bash
# 删除安装目录 (无需 sudo)
rm -rf ~/onetools

# 从 shell 配置文件中移除 PATH 配置
# 编辑 ~/.zshrc, ~/.bashrc, 或 ~/.bash_profile，删除包含 onetools 的 PATH 行
```

### Windows
```powershell
# 删除安装目录
Remove-Item -Path "$env:USERPROFILE\onetools" -Recurse -Force

# 从环境变量中移除路径（需要手动编辑环境变量）
# 1. 读取用户级 PATH
$path = [Environment]::GetEnvironmentVariable("PATH", "User")
# 2. 删除指定路径
$newPath = ($path -split ";") | Where-Object { $_ -ne "C:\Users\<USER>\onetools" }
# 3.重新写回 PATH
[Environment]::SetEnvironmentVariable("PATH", ($newPath -join ";"), "User")
```

## 常见问题

### macOS 问题

**Q: 提示权限不足怎么办？**
A: 脚本会自动使用 sudo 获取管理员权限，按提示输入密码即可。

**Q: 安装后找不到 onetools 命令？**
A: 重新打开终端，或运行 ` source ~/.zshrc `

**Q: curl 证书错误？**
A: 可以使用 `curl -fsSL -k` 跳过证书验证（不推荐）

### Windows 问题

**Q: 批处理脚本提示"需要 curl 或 PowerShell"？**
A: 
- Windows 10 1803+ 用户：系统应该内置 curl，请检查是否被禁用
- 较老版本 Windows：安装 curl 或确保 PowerShell 可用
- 企业环境：联系管理员安装必要工具

**Q: 批处理脚本提示"需要解压工具"？**
A:
- Windows 10 1803+ 用户：使用内置 tar 命令
- 较老版本 Windows：安装 .NET Framework 4.5+ 以支持 PowerShell 解压
- 或手动下载 ZIP 文件并解压到 `%USERPROFILE%\onetools`

**Q: PowerShell 执行策略限制？**
A: 运行 `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

**Q: 安装后无法使用 onetools 命令？**
A: 重新打开命令提示符或 PowerShell，环境变量需要重新加载


