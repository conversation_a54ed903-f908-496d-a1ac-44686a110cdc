# onetools Windows Automatic Installation Script
# Usage: 
# PowerShell: iex ((New-Object System.Net.WebClient).DownloadString('https://your-domain.com/install_windows.ps1'))
# Or: Invoke-WebRequest -Uri "https://your-domain.com/install_windows.ps1" -UseBasicParsing | Invoke-Expression

# Set error handling
$ErrorActionPreference = "Stop"

# Configuration
$VERSION_URL = "https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/version.json"
$ONETOOLS_HOME = "$env:USERPROFILE\onetools"
$TEMP_DIR = "$env:TEMP\onetools_install"

# Color definitions
function Print-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Print-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Print-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Print-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if command exists
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Check system requirements
function Test-Requirements {
    Print-Info "Checking system requirements..."
    
    # Check operating system
    if ($env:OS -ne "Windows_NT") {
        Print-Error "This script only supports Windows systems"
        exit 1
    }
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 3) {
        Print-Error "PowerShell 3.0 or higher is required"
        exit 1
    }
    
    # Check .NET Framework (for extraction)
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
    } catch {
        Print-Error ".NET Framework 4.5 or higher is required"
        exit 1
    }
    
    Print-Success "System requirements check passed"
}

# Get currently installed version
function Get-CurrentVersion {
    if (Test-Command "onetools") {
        try {
            $versionOutput = & onetools version 2>$null
            if ($versionOutput -and $versionOutput -match "^(V[0-9]+(\.[0-9]+)*)") {
                return $matches[1]
            }
        } catch {
            # Ignore errors
        }
    }
    return ""
}

# Get latest version information
function Get-LatestVersionInfo {
    Print-Info "Getting latest version information..."
    
    try {
        $response = Invoke-WebRequest -Uri $VERSION_URL -UseBasicParsing
        $jsonData = $response.Content | ConvertFrom-Json
        
        $version = $jsonData.version
        $url = $jsonData.url
        
        if (-not $version -or -not $url) {
            Print-Error "Failed to parse version information"
            exit 1
        }
        
        # Extract version number from version field (e.g.: onetools_V1.1.0_8073195.zip -> V1.1.0)
        if ($version -match "(V[0-9.]+)") {
            $script:LATEST_VERSION = $matches[1]
            $script:DOWNLOAD_URL = $url
            
            if (-not $script:LATEST_VERSION) {
                Print-Error "Unable to parse latest version number"
                exit 1
            }
            
            Print-Success "Latest version: $script:LATEST_VERSION"
        } else {
            Print-Error "Unable to parse latest version number"
            exit 1
        }
    } catch {
        Print-Error "Unable to get version information"
        exit 1
    }
}

# Add to PATH environment variable
function Add-ToPath {
    param([string]$DirToAdd)
    
    # Check if already in PATH
    $currentUserPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    $currentSystemPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    $fullCurrentPath = "$currentUserPath;$currentSystemPath"
    
    if ($fullCurrentPath -like "*$DirToAdd*") {
        Print-Info "PATH already contains $DirToAdd"
        return
    }
    
    Print-Info "Adding $DirToAdd to PATH"
    
    # Add to user-level PATH environment variable
    $newPath = if ($currentUserPath) { "$currentUserPath;$DirToAdd" } else { $DirToAdd }
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    
    # Update current session PATH
    $env:PATH = "$env:PATH;$DirToAdd"
    
    Print-Success "Added $DirToAdd to PATH"
    Print-Info "To take effect immediately, please reopen PowerShell or Command Prompt"
}

# Download and install onetools
function Install-OneTools {
    Print-Info "Starting download and installation of onetools..."
    
    # Create temporary directory
    if (Test-Path $TEMP_DIR) {
        Remove-Item -Path $TEMP_DIR -Recurse -Force
    }
    New-Item -ItemType Directory -Path $TEMP_DIR -Force | Out-Null
    
    try {
        # Download file
        $zipFile = Join-Path $TEMP_DIR "onetools.zip"
        Print-Info "Downloading: $script:DOWNLOAD_URL"
        
        Invoke-WebRequest -Uri $script:DOWNLOAD_URL -OutFile $zipFile -UseBasicParsing
        
        # Extract file
        Print-Info "Extracting files..."
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory($zipFile, $TEMP_DIR)
        
        # Find extracted onetools directory
        $onetoolsSourceDir = Join-Path $TEMP_DIR "onetools"
        if (-not (Test-Path $onetoolsSourceDir)) {
            Print-Error "Extracted onetools directory not found"
            exit 1
        }
        
        # Verify required files exist
        $onetoolsBinary = Join-Path $onetoolsSourceDir "onetools.exe"
        if (-not (Test-Path $onetoolsBinary)) {
            Print-Error "onetools.exe executable not found"
            exit 1
        }
        
        # Create onetools main directory and copy all files
        Print-Info "Installing onetools to $ONETOOLS_HOME..."
        
        # Remove old installation directory (if exists)
        if (Test-Path $ONETOOLS_HOME) {
            Print-Info "Removing old version..."
            Remove-Item -Path $ONETOOLS_HOME -Recurse -Force
        }
        
        # Create directory and copy entire onetools directory
        $parentDir = Split-Path $ONETOOLS_HOME -Parent
        if (-not (Test-Path $parentDir)) {
            New-Item -ItemType Directory -Path $parentDir -Force | Out-Null
        }
        Copy-Item -Path $onetoolsSourceDir -Destination $ONETOOLS_HOME -Recurse -Force
        
        # Add to PATH environment variable
        Add-ToPath $ONETOOLS_HOME
        
        Print-Success "onetools has been successfully installed to:"
        Print-Info "  Program directory: $ONETOOLS_HOME"
        Print-Info "  Executable file: $(Join-Path $ONETOOLS_HOME 'onetools.exe')"
        
        # Show installed content
        $gradleDir = Join-Path $ONETOOLS_HOME "Gradle"
        if (Test-Path $gradleDir) {
            Print-Info "  Gradle directory: $gradleDir"
        }
        
    } finally {
        # Clean up temporary files
        if (Test-Path $TEMP_DIR) {
            Remove-Item -Path $TEMP_DIR -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
}

# Main function
function Main {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    onetools Windows Installation Script" -ForegroundColor Cyan  
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Check system requirements
    Test-Requirements
    
    # Get current version
    $currentVersion = Get-CurrentVersion
    if ($currentVersion) {
        Print-Info "Currently installed version: $currentVersion"
    } else {
        Print-Info "No installed onetools detected"
    }
    
    # Get latest version information
    Get-LatestVersionInfo
    
    # Compare versions
    if ($script:LATEST_VERSION -eq $currentVersion) {
        Print-Success "Already running the latest version ($currentVersion)"
        exit 0
    } else {
        Print-Info "Preparing to install onetools $script:LATEST_VERSION"
    }
    
    # Install onetools
    Install-OneTools
    
    # Verify installation
    if (Test-Command "onetools") {
        $installedVersion = Get-CurrentVersion
        Print-Success "Installation complete! Current version: $installedVersion"
        Print-Info "Use 'onetools --help' to view help information"
    } else {
        Print-Warning "Installation verification failed, please reopen PowerShell or Command Prompt"
        Print-Info "Or run the following command:"
        Print-Info "`$env:PATH = `"`$env:PATH;$ONETOOLS_HOME`""
    }
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "        Installation Complete!" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
}

# Run main function
try {
    Main
} catch {
    Print-Error "Error occurred during installation: $($_.Exception.Message)"
    exit 1
}