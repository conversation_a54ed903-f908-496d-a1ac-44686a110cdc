@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

REM onetools Windows Install Script
REM Usage: Run this batch file directly

REM Configuration
set "VERSION_URL=https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/version.json"
set "ONETOOLS_HOME=%USERPROFILE%\onetools"
set "TEMP_DIR=%TEMP%\onetools_install"

echo ========================================
echo        onetools Windows Install
echo ========================================
echo.

REM Check system requirements
call :check_requirements
if !errorlevel! neq 0 goto :error

REM Get current version
call :get_current_version
echo [INFO] Current installed version: !CURRENT_VERSION!

REM Get latest version info
call :get_latest_version_info
if !errorlevel! neq 0 goto :error

REM Compare versions
if "!LATEST_VERSION!"=="!CURRENT_VERSION!" (
    echo [SUCCESS] Already using latest version ^(!CURRENT_VERSION!^)
    goto :end
)

echo [INFO] Preparing to install onetools !LATEST_VERSION!

REM Install onetools
call :install_onetools
if !errorlevel! neq 0 goto :error

REM Verify installation
call :verify_installation

goto :end

REM ==================== Function Definitions ====================

:check_requirements
echo [INFO] Checking system requirements...

REM Check operating system
if not "%OS%"=="Windows_NT" (
    echo [ERROR] This script only supports Windows systems
    exit /b 1
)

REM Check if curl is available
curl --version >nul 2>&1
if !errorlevel! equ 0 (
    set "DOWNLOAD_METHOD=curl"
    echo [INFO] Using curl for downloads
    goto :check_extract
)

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if !errorlevel! equ 0 (
    set "DOWNLOAD_METHOD=powershell"
    echo [INFO] Using PowerShell for downloads
    goto :check_extract
)

echo [ERROR] Need curl / PowerShell to download files
echo [INFO] Please install curl / PowerShell and try again
exit /b 1

:check_extract
REM Check extraction tools
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem" >nul 2>&1
if !errorlevel! equ 0 (
    set "EXTRACT_METHOD=powershell"
    echo [INFO] Using PowerShell for extraction
    goto :requirements_ok
)

REM Check if tar command is available
tar --version >nul 2>&1
if !errorlevel! equ 0 (
    set "EXTRACT_METHOD=tar"
    echo [INFO] Using tar for extraction
    goto :requirements_ok
)

echo [ERROR] Need PowerShell (.NET Framework 4.5+) / tar to extract files
echo [INFO] Please upgrade to Windows 10 1803+ / install .NET Framework 4.5+
exit /b 1

:requirements_ok
echo [SUCCESS] System requirements check passed
exit /b 0

:get_current_version
set "CURRENT_VERSION="
onetools version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=*" %%i in ('onetools version 2^>nul') do (
        set "version_line=%%i"
        REM Extract version number (format: V1.1.0)
        for /f "tokens=1" %%j in ("!version_line!") do (
            set "potential_version=%%j"
            for /f "tokens=1 delims=_" %%k in ("!potential_version!") do (
                set "CURRENT_VERSION=%%k"
            )
        )
    )
)
if "!CURRENT_VERSION!"=="" (
    set "CURRENT_VERSION=Not installed"
)
exit /b 0

:get_latest_version_info
echo [INFO] Getting latest version info...

REM Create temporary file
set "VERSION_FILE=%TEMP%\onetools_version.json"
if exist "!VERSION_FILE!" del "!VERSION_FILE!"

REM Download version info
if "!DOWNLOAD_METHOD!"=="curl" (
    curl -s -o "!VERSION_FILE!" "!VERSION_URL!"
) else (
    powershell -Command "try { (New-Object System.Net.WebClient).DownloadFile('!VERSION_URL!', '!VERSION_FILE!') } catch { exit 1 }"
)

if !errorlevel! neq 0 (
    echo [ERROR] Unable to download version info
    exit /b 1
)

if not exist "!VERSION_FILE!" (
    echo [ERROR] Version info file download failed
    exit /b 1
)

REM Parse JSON using PowerShell for better reliability
set "LATEST_VERSION="
set "DOWNLOAD_URL="

REM Use PowerShell to parse JSON safely
for /f "usebackq tokens=*" %%i in (`powershell -Command "try { $json = Get-Content '!VERSION_FILE!' | ConvertFrom-Json; Write-Output $json.version } catch { Write-Output 'ERROR' }"`) do (
    set "json_version=%%i"
)

for /f "usebackq tokens=*" %%i in (`powershell -Command "try { $json = Get-Content '!VERSION_FILE!' | ConvertFrom-Json; Write-Output $json.url } catch { Write-Output 'ERROR' }"`) do (
    set "json_url=%%i"
)

REM Extract version number from filename (e.g., onetools_V1.1.0_8073195.zip -> V1.1.0)
if not "!json_version!"=="ERROR" (
    for /f "tokens=2 delims=_" %%k in ("!json_version!") do (
        set "potential_version=%%k"
        echo !potential_version! | findstr /r "^V[0-9][0-9]*\.[0-9][0-9]*" >nul
        if !errorlevel! equ 0 (
            set "LATEST_VERSION=!potential_version!"
        )
    )
)

if not "!json_url!"=="ERROR" (
    set "DOWNLOAD_URL=!json_url!"
)

REM Clean up temporary file
if exist "!VERSION_FILE!" del "!VERSION_FILE!"

if "!LATEST_VERSION!"=="" (
    echo [ERROR] Unable to parse latest version number
    exit /b 1
)

if "!DOWNLOAD_URL!"=="" (
    echo [ERROR] Unable to parse download link
    exit /b 1
)

echo [SUCCESS] Latest version: !LATEST_VERSION!
exit /b 0

:install_onetools
echo [INFO] Starting download and installation...

REM Create temporary directory
if exist "!TEMP_DIR!" rmdir /s /q "!TEMP_DIR!"
mkdir "!TEMP_DIR!"

set "ZIP_FILE=!TEMP_DIR!\onetools.zip"

REM Download file
echo [INFO] Downloading: !DOWNLOAD_URL!
if "!DOWNLOAD_METHOD!"=="curl" (
    curl -L -o "!ZIP_FILE!" "!DOWNLOAD_URL!"
) else (
    powershell -Command "try { (New-Object System.Net.WebClient).DownloadFile('!DOWNLOAD_URL!', '!ZIP_FILE!') } catch { exit 1 }"
)

if !errorlevel! neq 0 (
    echo [ERROR] Download failed
    exit /b 1
)

if not exist "!ZIP_FILE!" (
    echo [ERROR] Downloaded file does not exist
    exit /b 1
)

REM Extract files
echo [INFO] Extracting files...
if "!EXTRACT_METHOD!"=="powershell" (
    powershell -Command "try { Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('!ZIP_FILE!', '!TEMP_DIR!') } catch { exit 1 }"
) else (
    cd /d "!TEMP_DIR!"
    tar -xf "!ZIP_FILE!"
    cd /d "%~dp0"
)

if !errorlevel! neq 0 (
    echo [ERROR] Extraction failed
    exit /b 1
)

REM Find extracted onetools directory
set "ONETOOLS_SOURCE_DIR=!TEMP_DIR!\onetools"
if not exist "!ONETOOLS_SOURCE_DIR!" (
    echo [ERROR] Extracted onetools directory not found
    exit /b 1
)

REM Verify required files exist
set "ONETOOLS_BINARY=!ONETOOLS_SOURCE_DIR!\onetools.exe"
if not exist "!ONETOOLS_BINARY!" (
    echo [ERROR] onetools.exe executable not found
    exit /b 1
)

REM Install to target directory
echo [INFO] Installing onetools to !ONETOOLS_HOME!...

REM Remove old installation directory (if exists)
if exist "!ONETOOLS_HOME!" (
    echo [INFO] Removing old version...
    rmdir /s /q "!ONETOOLS_HOME!"
)

REM Create directory and copy files
mkdir "!ONETOOLS_HOME!"
xcopy "!ONETOOLS_SOURCE_DIR!\*" "!ONETOOLS_HOME!\" /E /I /H /Y >nul

if !errorlevel! neq 0 (
    echo [ERROR] File copy failed
    exit /b 1
)

REM Add to PATH environment variable
call :add_to_path "!ONETOOLS_HOME!"

echo [SUCCESS] onetools successfully installed to:
echo [INFO]   Program directory: !ONETOOLS_HOME!
echo [INFO]   Executable file: !ONETOOLS_HOME!\onetools.exe

REM Display installed content
if exist "!ONETOOLS_HOME!\Gradle" (
    echo [INFO]   Gradle directory: !ONETOOLS_HOME!\Gradle
)

REM Clean up temporary files
if exist "!TEMP_DIR!" rmdir /s /q "!TEMP_DIR!"

exit /b 0

:add_to_path
set "DIR_TO_ADD=%~1"

REM Check if already in current session PATH
echo !PATH! | findstr /i "!DIR_TO_ADD!" >nul
if !errorlevel! equ 0 (
    echo [INFO] PATH already contains !DIR_TO_ADD!
    exit /b 0
)

echo [INFO] Adding !DIR_TO_ADD! to PATH

REM Get current user PATH from registry
set "USER_PATH="
for /f "usebackq tokens=2*" %%i in (`reg query "HKCU\Environment" /v PATH 2^>nul`) do (
    set "USER_PATH=%%j"
)

REM Check if already in user PATH
if not "!USER_PATH!"=="" (
    echo !USER_PATH! | findstr /i "!DIR_TO_ADD!" >nul
    if !errorlevel! equ 0 (
        echo [INFO] User PATH already contains !DIR_TO_ADD!, updating current session only
        goto :update_current_session
    )
)

REM If user PATH is empty, set directly
if "!USER_PATH!"=="" (
    set "NEW_PATH=!DIR_TO_ADD!"
) else (
    set "NEW_PATH=!USER_PATH!;!DIR_TO_ADD!"
)

REM Update user PATH in registry
reg add "HKCU\Environment" /v PATH /t REG_EXPAND_SZ /d "!NEW_PATH!" /f >nul
if !errorlevel! neq 0 (
    echo [WARNING] Failed to update user PATH in registry
    goto :update_current_session
)

REM Broadcast environment change message using PowerShell
powershell -Command "try { Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport(\"user32.dll\", SetLastError = true, CharSet = CharSet.Auto)] public static extern IntPtr SendMessageTimeout(IntPtr hWnd, uint Msg, UIntPtr wParam, string lParam, uint fuFlags, uint uTimeout, out UIntPtr lpdwResult); }'; $HWND_BROADCAST = [IntPtr]0xffff; $WM_SETTINGCHANGE = 0x1a; $result = [UIntPtr]::Zero; [Win32]::SendMessageTimeout($HWND_BROADCAST, $WM_SETTINGCHANGE, [UIntPtr]::Zero, 'Environment', 2, 5000, [ref]$result) } catch { }"

:update_current_session
REM Update current session PATH using proper variable expansion
set "PATH=!PATH!;!DIR_TO_ADD!"

REM Verify the update worked
echo !PATH! | findstr /i "!DIR_TO_ADD!" >nul
if !errorlevel! equ 0 (
    echo [SUCCESS] Added !DIR_TO_ADD! to PATH
    echo [INFO] PATH updated for current session and future sessions
) else (
    echo [WARNING] Failed to update current session PATH
    echo [INFO] PATH will be available in new command prompt windows
)

exit /b 0

:verify_installation
onetools version >nul 2>&1
if !errorlevel! equ 0 (
    call :get_current_version
    echo [SUCCESS] Installation complete! Current version: !CURRENT_VERSION!
    echo [INFO] Use 'onetools --help' to view help information
) else (
    echo [WARNING] Installation verification failed, please reopen command prompt
    echo [INFO] Manual path addition may be needed: !ONETOOLS_HOME!
)
exit /b 0

:error
echo.
echo [ERROR] Installation failed
if exist "!TEMP_DIR!" rmdir /s /q "!TEMP_DIR!"
pause
exit /b 1

:end
echo.
echo ========================================
echo            Installation Complete!
echo ========================================
echo.
echo For help, run: onetools --help
pause
