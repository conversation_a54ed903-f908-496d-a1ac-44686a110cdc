#!/bin/bash

# onetools macOS 自动安装脚本
# 使用方法: curl -fsSL https://your-domain.com/install_macos.sh | bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
VERSION_URL="https://download.wmupd.com:47317/gameResource/Generated/1001/iOS_Onetools_cmdline1/version.json"
ONETOOLS_HOME="$HOME/onetools"
TEMP_DIR="/tmp/onetools_install"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."
    
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "此脚本仅支持 macOS 系统"
        exit 1
    fi
    
    if ! command_exists curl; then
        print_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command_exists unzip; then
        print_error "unzip 未安装，请先安装 unzip"
        exit 1
    fi
    
    print_success "系统要求检查通过"
}

# 获取当前安装的版本
get_current_version() {
    if command_exists onetools; then
        local version_output=$(onetools version 2>/dev/null || echo "")
        if [[ -n "$version_output" ]]; then
            # 从输出中提取版本号 (格式: V1.0.17_3a1efbd)
            echo "$version_output" | sed -En 's/^(V[0-9]+(\.[0-9]+)*).*/\1/p'
        else
            echo ""
        fi
    else
        echo ""
    fi
}

# 获取最新版本信息
get_latest_version_info() {
    print_info "获取最新版本信息..."
    
    local response=$(curl -fsSL "$VERSION_URL" 2>/dev/null)
    if [[ $? -ne 0 ]]; then
        print_error "无法获取版本信息"
        exit 1
    fi
    
    # 解析 JSON 响应
    local version=$(echo "$response" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    local url=$(echo "$response" | grep -o '"url":"[^"]*"' | cut -d'"' -f4)
    
    if [[ -z "$version" || -z "$url" ]]; then
        print_error "版本信息解析失败"
        exit 1
    fi
    
    # 从 version 字段提取版本号 (例如: onetools_V1.1.0_8073195.zip -> 1.1.0)
    LATEST_VERSION=$(echo "$version" | sed -n 's/.*\(V[0-9.]*\).*/\1/p')
    DOWNLOAD_URL="$url"
    
    if [[ -z "$LATEST_VERSION" ]]; then
        print_error "无法解析最新版本号"
        exit 1
    fi
    
    print_success "最新版本: $LATEST_VERSION"
}

# 添加到 PATH 环境变量
add_to_path() {
    local dir_to_add="$1"
    
    # 检查是否已经在 PATH 中
    if [[ ":$PATH:" == *":$dir_to_add:"* ]]; then
        print_info "PATH 中已包含 $dir_to_add"
        return 0
    fi
    
    local config_file=""
    
    # 根据当前 shell 和环境选择配置文件
    # 优先检查 SHELL 环境变量，然后检查版本变量
    if [[ "$SHELL" == */zsh ]] || [[ -n "$ZSH_VERSION" ]]; then
        config_file="$HOME/.zshrc"
        print_info "检测到 Zsh shell，使用 ~/.zshrc"
    elif [[ "$SHELL" == */bash ]] || [[ -n "$BASH_VERSION" ]]; then
        # 在 macOS 上优先使用 .bash_profile
        if [[ "$OSTYPE" == "darwin"* ]]; then
            if [[ -f "$HOME/.bash_profile" ]]; then
                config_file="$HOME/.bash_profile"
                print_info "检测到 Bash shell (macOS)，使用现有的 ~/.bash_profile"
            else
                config_file="$HOME/.bash_profile"
                print_info "检测到 Bash shell (macOS)，将创建 ~/.bash_profile"
            fi
        else
            # Linux 系统优先使用 .bashrc
            if [[ -f "$HOME/.bashrc" ]]; then
                config_file="$HOME/.bashrc"
                print_info "检测到 Bash shell，使用现有的 ~/.bashrc"
            else
                config_file="$HOME/.bashrc"
                print_info "检测到 Bash shell，将创建 ~/.bashrc"
            fi
        fi
    else
        # 默认使用 .profile
        config_file="$HOME/.profile"
        print_info "未知 shell，使用默认配置文件 ~/.profile"
    fi
    
    # 创建配置文件（如果不存在）
    touch "$config_file"
    
    # 检查是否已经添加过（使用更精确的匹配）
    if grep -q "# Added by onetools installer" "$config_file" && grep -q "$dir_to_add" "$config_file"; then
        print_info "PATH 配置已存在于 $config_file"
        return 0
    fi
    
    # 添加到配置文件
    echo "" >> "$config_file"
    echo "# Added by onetools installer" >> "$config_file"
    echo "export PATH=\"$dir_to_add:\$PATH\"" >> "$config_file"
    
    # 更新当前会话的 PATH
    export PATH="$dir_to_add:$PATH"
    
    print_success "已添加 $dir_to_add 到 PATH"
    print_info "配置已写入 $config_file"
    print_info "要立即生效，请运行: source $config_file"
}

# 下载并安装 onetools
install_onetools() {
    print_info "开始下载和安装 onetools..."
    
    # 创建临时目录
    rm -rf "$TEMP_DIR"
    mkdir -p "$TEMP_DIR"
    
    # 下载文件
    local zip_file="$TEMP_DIR/onetools.zip"
    print_info "正在下载: $DOWNLOAD_URL"
    
    if ! curl -fsSL -o "$zip_file" "$DOWNLOAD_URL"; then
        print_error "下载失败"
        exit 1
    fi
    
    # 解压文件
    print_info "正在解压文件..."
    if ! unzip -q "$zip_file" -d "$TEMP_DIR"; then
        print_error "解压失败"
        exit 1
    fi
    
    # 查找解压后的 onetools 目录
    local onetools_source_dir="$TEMP_DIR/onetools"
    if [[ ! -d "$onetools_source_dir" ]]; then
        print_error "未找到解压后的 onetools 目录"
        exit 1
    fi
    
    # 验证必要文件存在
    local onetools_binary="$onetools_source_dir/onetools"
    if [[ ! -f "$onetools_binary" ]]; then
        print_error "未找到 onetools 可执行文件"
        exit 1
    fi
    
    # 创建 onetools 主目录并复制所有文件
    print_info "正在安装 onetools 到 $ONETOOLS_HOME..."
    
    # 删除旧的安装目录（如果存在）
    if [[ -d "$ONETOOLS_HOME" ]]; then
        print_info "删除旧版本..."
        rm -rf "$ONETOOLS_HOME"
    fi
    
    # 创建目录并复制整个 onetools 目录
    mkdir -p "$(dirname "$ONETOOLS_HOME")"
    cp -r "$onetools_source_dir" "$ONETOOLS_HOME"
    
    # 设置执行权限
    chmod +x "$ONETOOLS_HOME/onetools"
    
    # 如果存在 Gradle 目录，设置 gradlew 执行权限
    if [[ -f "$ONETOOLS_HOME/Gradle/gradlew" ]]; then
        chmod +x "$ONETOOLS_HOME/Gradle/gradlew"
        print_success "已设置 Gradle/gradlew 执行权限"
    fi
    
    # 清理临时文件
    rm -rf "$TEMP_DIR"
    
    # 添加到 PATH 环境变量
    add_to_path "$ONETOOLS_HOME"
    
    print_success "onetools 已成功安装到:"
    print_info "  程序目录: $ONETOOLS_HOME"
    print_info "  可执行文件: $ONETOOLS_HOME/onetools"
    
    # 显示安装的内容
    if [[ -d "$ONETOOLS_HOME/Gradle" ]]; then
        print_info "  Gradle 目录: $ONETOOLS_HOME/Gradle"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "       onetools macOS 自动安装脚本"
    echo "========================================"
    echo
    
    # 检查系统要求
    check_requirements
    
    # 获取当前版本
    CURRENT_VERSION=$(get_current_version)
    if [[ -n "$CURRENT_VERSION" ]]; then
        print_info "当前已安装版本: $CURRENT_VERSION"
    else
        print_info "未检测到已安装的 onetools"
    fi
    
    # 获取最新版本信息
    get_latest_version_info
    
    # 比较版本"$LATEST_VERSION" "$CURRENT_VERSION"
     if [[ "$LATEST_VERSION" == "$CURRENT_VERSION" ]]; then
        print_success "当前已是最新版本 ($CURRENT_VERSION)"
        exit 0
    else
        print_info "准备安装 onetools $LATEST_VERSION"
    fi
    
    # 安装 onetools
    install_onetools
    
    # 验证安装
    if command_exists onetools; then
        local installed_version=$(get_current_version)
        print_success "安装完成! 当前版本: $installed_version"
        print_info "使用 'onetools --help' 查看帮助信息"
    else
        print_warning "安装验证失败，请重新打开终端或运行以下命令:"
        print_info "export PATH=\"$ONETOOLS_HOME:\$PATH\""
    fi
    
    echo
    echo "========================================"
    echo "           安装完成!"
    echo "========================================"
}

# 运行主函数
main "$@"